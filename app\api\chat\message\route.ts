import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { dbService } from '@/lib/db-service'
import { AIProviderFactory } from '@/lib/ai'
import { BloomLevel, memoryService } from '@/lib/ai/memory-service'

/**
 * Chat Message API Endpoint
 * Handles non-streaming AI chat requests
 */

interface ChatMessageRequest {
  documentId: string
  chunkIndex: number
  message: string
  provider?: 'openai' | 'anthropic'
  bloomLevel?: BloomLevel
}

/**
 * POST /api/chat/message
 * Send message and get complete AI response (non-streaming)
 */
export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const supabase = await createClient()
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Parse request body
    const { 
      documentId, 
      chunkIndex, 
      message, 
      bloomLevel 
    }: ChatMessageRequest = await request.json()

    // Validate required fields
    if (!documentId || chunkIndex === undefined || !message) {
      return NextResponse.json(
        { error: 'Missing required fields: documentId, chunkIndex, message' },
        { status: 400 }
      )
    }

    // Verify document exists and belongs to user
    const document = await dbService.document.findFirst({
      where: {
        id: documentId,
        userId: user.id
      }
    })

    if (!document) {
      return NextResponse.json(
        { error: 'Document not found or access denied' },
        { status: 404 }
      )
    }

    // Verify document is ready
    if (document.status !== 'READY') {
      return NextResponse.json(
        { error: 'Document is not ready for chat' },
        { status: 400 }
      )
    }

    // Get the current chunk
    const chunk = await dbService.chunk.findUnique({
      where: {
        documentId_chunkIndex: {
          documentId,
          chunkIndex
        }
      }
    })

    if (!chunk) {
      return NextResponse.json(
        { error: 'Chunk not found' },
        { status: 404 }
      )
    }

    // Get user's progress to find session ID
    const progress = await dbService.progress.findUnique({
      where: {
        userId_documentId: {
          userId: user.id,
          documentId
        }
      }
    })

    if (!progress?.sessionId) {
      return NextResponse.json(
        { 
          error: 'AI session not initialized. Please initialize AI first.',
          needsInitialization: true 
        },
        { status: 400 }
      )
    }

    // Ensure sessionId is not null (TypeScript safety)
    const sessionId = progress.sessionId
    if (!sessionId) {
      return NextResponse.json(
        { 
          error: 'Invalid session ID. Please initialize AI first.',
          needsInitialization: true 
        },
        { status: 400 }
      )
    }

    // Create AI provider
    const aiProvider = AIProviderFactory.createProvider()
    await aiProvider.initialize()

    // Check if this is the initial chunk processing (special case)
    const isInitialChunkProcessing = message.includes(chunk.content) && message.length > chunk.content.length * 0.8

    let prompt = message
    if (isInitialChunkProcessing) {
      // This is the first chunk being sent for AI processing
      console.log('🤖 Processing initial chunk for AI response')
      prompt = `You are an expert AI tutor using the Socratic method to guide learning. A student is starting to learn from a document titled "${document.fileName}".

The student has just been presented with the first section of the document. Your role is to:

1. Welcome them warmly to the learning session
2. Briefly explain how you'll guide them using the Socratic method
3. Ask a thoughtful opening question about the main concept or key idea in this section
4. Encourage them to think deeply rather than just recall facts

Keep your response engaging, encouraging, and focused on helping them discover insights through questioning.

Document section content:
${chunk.content}

Generate a welcoming response that starts their learning journey.`
    }

    // Build conversation context with document-based memory
    const context = {
      userId: user.id,
      documentId,
      chunkIndex,
      chunkContent: chunk.content,
      sessionId, // Now guaranteed to be string, not null
      currentQuery: prompt,
      bloomLevel
    }

    // Generate complete response
    let fullResponse = ''
    const responseGenerator = aiProvider.generateResponse(prompt, context)

    for await (const token of responseGenerator) {
      fullResponse += token
    }

    // Store the conversation in document-based memory
    try {
      if (isInitialChunkProcessing) {
        // Store the chunk content as user message
        await memoryService.addConversationMessage(
          documentId,
          user.id,
          chunk.content,
          'user',
          chunkIndex,
          true // isInitialMessage
        )
      } else {
        // Store the user's actual message
        await memoryService.addConversationMessage(
          documentId,
          user.id,
          message,
          'user',
          chunkIndex,
          false
        )
      }

      // Store the AI response
      await memoryService.addConversationMessage(
        documentId,
        user.id,
        fullResponse,
        'assistant',
        chunkIndex,
        isInitialChunkProcessing
      )

      console.log('✅ Stored conversation in document-based memory')
    } catch (memoryError) {
      console.warn('⚠️ Failed to store conversation in memory:', memoryError)
      // Continue without failing the request
    }

    // Return complete response
    return NextResponse.json({
      success: true,
      response: fullResponse,
      messageId: `msg_${Date.now()}`,
      timestamp: new Date().toISOString(),
      chunkIndex,
      documentId,
      isInitialResponse: isInitialChunkProcessing
    })

  } catch (error) {
    console.error('Chat message API error:', error)
    
    return NextResponse.json(
      { 
        error: 'Failed to process chat message',
        details: error instanceof Error ? error.message : 'Unknown error'
      }, 
      { status: 500 }
    )
  }
}
