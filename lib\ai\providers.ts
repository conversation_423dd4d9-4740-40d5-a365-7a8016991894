import OpenAI from 'openai'
import Anthropic from '@anthropic-ai/sdk'
import { BloomLevel } from './memory-service'
import { buildAdvancedTutoringSystemPrompt } from './prompts'
import { buildEducationalContext, storeConversationMemory } from './memory-integration'

/**
 * AI Provider types and interfaces
 * Supports OpenAI and Anthropic with streaming responses
 */

export type AIProviderType = 'openai' | 'anthropic'

/**
 * Context information passed to AI providers for generating responses
 * Contains all necessary information for educational tutoring
 */
export interface ConversationContext {
    userId: string           // User identifier for memory tracking
    documentId: string       // Document being studied
    chunkIndex: number       // Current chunk position in document
    chunkContent: string     // Content of current chunk
    sessionId: string        // Memory session identifier
    currentQuery: string     // User's current question/message
    bloomLevel?: BloomLevel  // Optional Bloom's taxonomy level for targeted learning
}

/**
 * Interface that all AI providers must implement
 * Provides consistent API for different AI services
 */
export interface AIProvider {
    name: AIProviderType
    generateResponse(prompt: string, context: ConversationContext): AsyncGenerator<string>
    initialize(): Promise<void>
}

/**
 * OpenAI provider implementation with streaming support
 * Uses GPT models for educational tutoring responses
 */
export class OpenAIProvider implements AIProvider {
    name: AIProviderType = 'openai'
    private client: OpenAI

    constructor() {
        this.client = new OpenAI({
            apiKey: process.env.OPENAI_API_KEY
        })
    }

    /**
     * Initialize the OpenAI provider and validate configuration
     * @throws Error if API key is not configured
     */
    async initialize(): Promise<void> {
        if (!process.env.OPENAI_API_KEY) {
            throw new Error('OPENAI_API_KEY is not configured')
        }
    }

    /**
     * Generate streaming response from OpenAI
     * Builds educational context, streams response, and stores in memory
     * 
     * @param prompt - User's message/question
     * @param context - Conversation context with document and memory info
     * @yields Streaming text tokens from AI response
     */
    async *generateResponse(prompt: string, context: ConversationContext): AsyncGenerator<string> {
        console.log(`🤖 OpenAI: Starting response generation for prompt: "${prompt.substring(0, 50)}..."`)
        
        try {
            // Build educational context using memory service
            console.log('🧠 OpenAI: Building educational context...')
            const educationalContext = await buildEducationalContext(context)
            console.log('✅ OpenAI: Educational context built successfully')
            
            const messages = this.buildMessages(prompt, context, educationalContext)
            console.log(`📝 OpenAI: Built ${messages.length} messages for API call`)

            // Create streaming completion request
            console.log(`🚀 OpenAI: Creating streaming completion with model: ${process.env.OPENAI_MODEL || 'gpt-4'}`)
            
            const stream = await this.client.chat.completions.create({
                model: process.env.OPENAI_MODEL || 'gpt-4',
                messages,
                stream: true,
                temperature: parseFloat(process.env.OPENAI_TEMPERATURE || '0.7'),
                max_tokens: parseInt(process.env.OPENAI_MAX_TOKENS || '1000')
            })
            
            console.log('✅ OpenAI: Stream created successfully, starting token generation...')

            // Stream response tokens and collect full response
            let fullResponse = ''
            let tokenCount = 0
            
            for await (const chunk of stream) {
                const content = chunk.choices[0]?.delta?.content
                if (content) {
                    fullResponse += content
                    tokenCount++
                    yield content
                }
            }
            
            console.log(`✅ OpenAI: Streaming completed. Tokens: ${tokenCount}, Response length: ${fullResponse.length}`)

            // Store complete conversation in memory after streaming
            if (fullResponse) {
                console.log('💾 OpenAI: Storing conversation in memory...')
                await storeConversationMemory(context, prompt, fullResponse)
                console.log('✅ OpenAI: Conversation stored successfully')
            }
        } catch (error) {
            console.error('❌ OpenAI: Error during response generation:', error)
            throw error
        }
    }

    /**
     * Build message array for OpenAI API
     * Combines system prompt with user message
     * 
     * @param prompt - User's message
     * @param context - Conversation context
     * @param educationalContext - Context from memory service
     * @returns Message array for OpenAI API
     */
    private buildMessages(prompt: string, context: ConversationContext, educationalContext: string) {
        const systemPrompt = buildAdvancedTutoringSystemPrompt(
            educationalContext,
            context.bloomLevel,
            undefined, // Strategy is embedded in educationalContext
            context.chunkIndex,
            undefined // Total chunks not available in context
        )

        return [
            { role: 'system' as const, content: systemPrompt },
            { role: 'user' as const, content: prompt }
        ]
    }
}

/**
 * Anthropic provider implementation with streaming support
 * Uses Claude models for educational tutoring responses
 */
export class AnthropicProvider implements AIProvider {
    name: AIProviderType = 'anthropic'
    private client: Anthropic

    constructor() {
        this.client = new Anthropic({
            apiKey: process.env.ANTHROPIC_API_KEY
        })
    }

    /**
     * Initialize the Anthropic provider and validate configuration
     * @throws Error if API key is not configured
     */
    async initialize(): Promise<void> {
        if (!process.env.ANTHROPIC_API_KEY) {
            throw new Error('ANTHROPIC_API_KEY is not configured')
        }
    }

    /**
     * Generate streaming response from Anthropic
     * Builds educational context, streams response, and stores in memory
     * 
     * @param prompt - User's message/question
     * @param context - Conversation context with document and memory info
     * @yields Streaming text tokens from AI response
     */
    async *generateResponse(prompt: string, context: ConversationContext): AsyncGenerator<string> {
        // Build educational context using memory service
        const educationalContext = await buildEducationalContext(context)
        const messages = this.buildMessages(prompt, context, educationalContext)

        // Create streaming completion request
        const stream = await this.client.messages.create({
            model: process.env.ANTHROPIC_MODEL || 'claude-3-sonnet-20240229',
            messages,
            stream: true,
            max_tokens: parseInt(process.env.ANTHROPIC_MAX_TOKENS || '1000'),
            temperature: parseFloat(process.env.ANTHROPIC_TEMPERATURE || '0.7')
        })

        // Stream response tokens and collect full response
        let fullResponse = ''
        for await (const chunk of stream) {
            if (chunk.type === 'content_block_delta' && chunk.delta.type === 'text_delta') {
                fullResponse += chunk.delta.text
                yield chunk.delta.text
            }
        }

        // Store complete conversation in memory after streaming
        if (fullResponse) {
            await storeConversationMemory(context, prompt, fullResponse)
        }
    }

    /**
     * Build message array for Anthropic API
     * Anthropic uses different message format than OpenAI
     * 
     * @param prompt - User's message
     * @param context - Conversation context
     * @param educationalContext - Context from memory service
     * @returns Message array for Anthropic API
     */
    private buildMessages(prompt: string, context: ConversationContext, educationalContext: string) {
        const systemPrompt = buildAdvancedTutoringSystemPrompt(
            educationalContext,
            context.bloomLevel,
            undefined, // Strategy is embedded in educationalContext
            context.chunkIndex,
            undefined // Total chunks not available in context
        )

        // Anthropic combines system prompt with user message
        return [
            { role: 'user' as const, content: `${systemPrompt}\n\nStudent message: ${prompt}` }
        ]
    }
}

/**
 * Factory class for creating AI provider instances
 * Handles provider selection based on environment configuration
 */
export class AIProviderFactory {
    /**
     * Create an AI provider instance based on environment configuration
     * @returns Configured AI provider instance
     * @throws Error if unsupported provider type is specified
     */
    static createProvider(): AIProvider {
        const providerType = (process.env.AI_PROVIDER as AIProviderType) || 'openai'

        switch (providerType) {
            case 'openai':
                return new OpenAIProvider()
            case 'anthropic':
                return new AnthropicProvider()
            default:
                throw new Error(`Unsupported AI provider: ${providerType}`)
        }
    }

    /**
     * Get the currently configured provider type
     * @returns Current provider type from environment
     */
    static getProviderType(): AIProviderType {
        return (process.env.AI_PROVIDER as AIProviderType) || 'openai'
    }
}
