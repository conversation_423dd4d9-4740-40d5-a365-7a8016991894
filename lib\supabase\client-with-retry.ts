import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'

interface RetryOptions {
  maxRetries: number
  baseDelay: number
  maxDelay: number
}

const DEFAULT_RETRY_OPTIONS: RetryOptions = {
  maxRetries: 3,
  baseDelay: 1000, // 1 second
  maxDelay: 10000  // 10 seconds
}

/**
 * Enhanced Supabase client with retry logic and better error handling
 */
export async function createClientWithRetry(retryOptions: Partial<RetryOptions> = {}) {
  const options = { ...DEFAULT_RETRY_OPTIONS, ...retryOptions }
  
  const cookieStore = await cookies()

  const client = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return cookieStore.getAll()
        },
        setAll(cookiesToSet) {
          try {
            cookiesToSet.forEach(({ name, value, options }) =>
              cookieStore.set(name, value, options)
            )
          } catch {
            // The `setAll` method was called from a Server Component.
            // This can be ignored if you have middleware refreshing
            // user sessions.
          }
        },
      },
      global: {
        fetch: createRetryFetch(options)
      }
    }
  )

  return client
}

/**
 * Create a fetch function with retry logic
 */
function createRetryFetch(options: RetryOptions) {
  return async (url: RequestInfo | URL, init?: RequestInit): Promise<Response> => {
    let lastError: Error | null = null
    
    for (let attempt = 0; attempt <= options.maxRetries; attempt++) {
      try {
        // Add timeout to prevent hanging requests
        const controller = new AbortController()
        const timeoutId = setTimeout(() => controller.abort(), 60000) // 60 second timeout for slow connections
        
        const response = await fetch(url, {
          ...init,
          signal: controller.signal
        })
        
        clearTimeout(timeoutId)
        
        // If successful, return response
        if (response.ok || response.status < 500) {
          return response
        }
        
        // For 5xx errors, retry
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        
      } catch (error) {
        lastError = error as Error

        // Check if it's an abort error (timeout)
        if (error instanceof Error && error.name === 'AbortError') {
          console.warn(`Request timed out (attempt ${attempt + 1}/${options.maxRetries + 1})`)
        }

        // Don't retry on the last attempt
        if (attempt === options.maxRetries) {
          break
        }
        
        // Calculate delay with exponential backoff
        const delay = Math.min(
          options.baseDelay * Math.pow(2, attempt),
          options.maxDelay
        )
        
        console.warn(`Supabase request failed (attempt ${attempt + 1}/${options.maxRetries + 1}):`, error)
        console.warn(`Retrying in ${delay}ms...`)
        
        // Wait before retrying
        await new Promise(resolve => setTimeout(resolve, delay))
      }
    }
    
    // If all retries failed, throw the last error
    console.error('All Supabase retry attempts failed:', lastError)
    throw lastError || new Error('Unknown error during Supabase request')
  }
}

/**
 * Wrapper for auth operations with specific retry logic
 */
export async function withAuthRetry<T>(
  operation: () => Promise<T>,
  operationName: string = 'auth operation'
): Promise<T> {
  const maxRetries = 2
  let lastError: Error | null = null
  
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await operation()
    } catch (error) {
      lastError = error as Error
      
      // Don't retry on the last attempt
      if (attempt === maxRetries) {
        break
      }
      
      console.warn(`${operationName} failed (attempt ${attempt + 1}/${maxRetries + 1}):`, error)
      
      // Wait before retrying (shorter delay for auth)
      await new Promise(resolve => setTimeout(resolve, 1000 * (attempt + 1)))
    }
  }
  
  console.error(`${operationName} failed after ${maxRetries + 1} attempts:`, lastError)
  throw lastError || new Error(`${operationName} failed`)
}