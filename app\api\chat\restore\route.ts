import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { dbService } from '@/lib/db-service'
import { memoryService } from '@/lib/ai/memory-service'

/**
 * Chat Session Restoration API Endpoint
 * Handles restoring chat sessions across browser sessions
 */

interface RestoreRequest {
  documentId: string
  includeFullHistory?: boolean
}

/**
 * POST /api/chat/restore
 * Restore chat session for a document
 */
export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const supabase = await createClient()
    const {
      data: { session },
    } = await supabase.auth.getSession()

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Parse request body
    const { 
      documentId, 
      includeFullHistory = false 
    }: RestoreRequest = await request.json()

    if (!documentId) {
      return NextResponse.json(
        { error: 'Document ID is required' },
        { status: 400 }
      )
    }

    // Verify document exists and belongs to user
    const document = await dbService.document.findFirst({
      where: {
        id: documentId,
        userId: session.user.id
      }
    })

    if (!document) {
      return NextResponse.json(
        { error: 'Document not found or access denied' },
        { status: 404 }
      )
    }

    // Get user's session info
    const sessionInfo = await memoryService.getSessionInfo(session.user.id, documentId)
    
    if (!sessionInfo.sessionId || !sessionInfo.exists) {
      return NextResponse.json({ 
        restored: false, 
        message: 'No previous session found for this document',
        canInitialize: true
      })
    }

    // Get conversation history from memory
    const memories = await memoryService.getAllMemories(documentId, session.user.id)
    
    // Filter and format conversation messages
    const allConversationMessages = memories
      .filter((memory: any) => memory.metadata?.category === 'conversation')
      .sort((a: any, b: any) => 
        new Date(a.metadata?.timestamp || a.created_at).getTime() - 
        new Date(b.metadata?.timestamp || b.created_at).getTime()
      )
      .map((memory: any) => ({
        id: memory.id,
        role: memory.metadata?.role || 'assistant',
        content: memory.memory || memory.text,
        timestamp: memory.metadata?.timestamp || memory.created_at
      }))

    // Limit messages if not requesting full history
    const conversationHistory = includeFullHistory 
      ? allConversationMessages
      : allConversationMessages.slice(-50) // Last 50 messages

    // Get current progress
    const progress = await dbService.progress.findUnique({
      where: {
        userId_documentId: {
          userId: session.user.id,
          documentId
        }
      }
    })

    // Get learning progress summary
    const learningMemories = memories.filter((memory: any) => 
      memory.metadata?.type === 'learning_progress'
    )

    const conceptMemories = memories.filter((memory: any) => 
      memory.metadata?.category === 'concept_understanding'
    )

    // Return restoration data
    return NextResponse.json({
      restored: true,
      sessionId: sessionInfo.sessionId,
      messages: conversationHistory,
      currentChunk: progress?.currentChunk || 0,
      totalChunks: document.totalChunks,
      sessionMetadata: {
        lastActivity: memories[memories.length - 1]?.created_at,
        totalMessages: allConversationMessages.length,
        totalMemories: memories.length,
        learningProgressCount: learningMemories.length,
        conceptsTracked: conceptMemories.length,
        sessionStarted: memories[0]?.created_at
      },
      progress: {
        current: (progress?.currentChunk || 0) + 1,
        total: document.totalChunks,
        percentage: Math.round(((progress?.currentChunk || 0) + 1) / document.totalChunks * 100)
      }
    })

  } catch (error) {
    console.error('Session restoration error:', error)
    
    return NextResponse.json(
      {
        error: 'Failed to restore session',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

/**
 * GET /api/chat/restore?documentId=xxx
 * Check if a session can be restored for a document
 */
export async function GET(request: NextRequest) {
  try {
    // Authenticate user
    const supabase = await createClient()
    const {
      data: { session },
    } = await supabase.auth.getSession()

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get document ID from query parameters
    const { searchParams } = new URL(request.url)
    const documentId = searchParams.get('documentId')

    if (!documentId) {
      return NextResponse.json(
        { error: 'Document ID is required' },
        { status: 400 }
      )
    }

    // Check session info
    const sessionInfo = await memoryService.getSessionInfo(session.user.id, documentId)
    
    if (!sessionInfo.sessionId || !sessionInfo.exists) {
      return NextResponse.json({
        canRestore: false,
        message: 'No previous session found'
      })
    }

    // Get basic session metadata
    const memories = await memoryService.getAllMemories(documentId, session.user.id)
    const conversationCount = memories.filter((memory: any) => 
      memory.metadata?.category === 'conversation'
    ).length

    return NextResponse.json({
      canRestore: true,
      sessionId: sessionInfo.sessionId,
      messageCount: conversationCount,
      lastActivity: memories[memories.length - 1]?.created_at,
      sessionStarted: memories[0]?.created_at
    })

  } catch (error) {
    console.error('Session restoration check error:', error)
    
    return NextResponse.json(
      {
        error: 'Failed to check session restoration',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
