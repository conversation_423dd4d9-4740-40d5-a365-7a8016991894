import { ContextStrategy } from './context-engineering'
import { BloomLevel } from './memory-service'

/**
 * Intelligent context strategy selection
 * Automatically chooses optimal context strategy based on user behavior and learning patterns
 */

/**
 * User behavior indicators that influence context strategy selection
 */
interface UserBehaviorIndicators {
    hasRecentMisconceptions: boolean
    isStrugglingWithConcepts: boolean
    isInApplicationPhase: boolean
    isReviewingPreviousContent: boolean
    isExploringNewTopics: boolean
    questionComplexity: 'simple' | 'moderate' | 'complex'
    engagementLevel: 'low' | 'medium' | 'high'
}

/**
 * Context strategy selector class
 * Analyzes user patterns to select optimal context retrieval strategy
 */
export class ContextStrategySelector {

    /**
     * Select optimal context strategy based on user behavior and learning state
     * 
     * @param userQuery - Current user question/message
     * @param bloomLevel - Current Bloom's taxonomy level
     * @param sessionId - Memory session identifier
     * @param chunkIndex - Current chunk position
     * @returns Recommended context strategy
     */
    async selectOptimalStrategy(
        userQuery: string,
        bloomLevel?: BloomLevel,
        sessionId?: string,
        chunkIndex?: number
    ): Promise<ContextStrategy> {

        // Analyze user behavior indicators
        const indicators = await this.analyzeUserBehavior(userQuery, sessionId, chunkIndex)

        // Apply strategy selection rules
        return this.applySelectionRules(indicators, bloomLevel)
    }

    /**
     * Analyze user behavior to extract learning indicators
     * 
     * @param userQuery - Current user question
     * @param sessionId - Memory session identifier
     * @param chunkIndex - Current chunk position
     * @returns User behavior indicators
     */
    private async analyzeUserBehavior(
        userQuery: string,
        _sessionId?: string,
        _chunkIndex?: number
    ): Promise<UserBehaviorIndicators> {

        // Analyze question complexity
        const questionComplexity = this.analyzeQuestionComplexity(userQuery)

        // Analyze engagement level from query characteristics
        const engagementLevel = this.analyzeEngagementLevel(userQuery)

        // Check for misconception indicators in query
        const hasRecentMisconceptions = this.detectMisconceptionIndicators(userQuery)

        // Detect if user is struggling with concepts
        const isStrugglingWithConcepts = this.detectStruggleIndicators(userQuery)

        // Detect application-oriented queries
        const isInApplicationPhase = this.detectApplicationIndicators(userQuery)

        // Detect review-oriented behavior
        const isReviewingPreviousContent = this.detectReviewIndicators(userQuery)

        // Detect exploration/discovery behavior
        const isExploringNewTopics = this.detectExplorationIndicators(userQuery)

        return {
            hasRecentMisconceptions,
            isStrugglingWithConcepts,
            isInApplicationPhase,
            isReviewingPreviousContent,
            isExploringNewTopics,
            questionComplexity,
            engagementLevel
        }
    }

    /**
     * Apply strategy selection rules based on behavior indicators
     * 
     * @param indicators - User behavior indicators
     * @param bloomLevel - Current Bloom's taxonomy level
     * @returns Selected context strategy
     */
    private applySelectionRules(
        indicators: UserBehaviorIndicators,
        bloomLevel?: BloomLevel
    ): ContextStrategy {

        // Priority 1: Address misconceptions immediately
        if (indicators.hasRecentMisconceptions || indicators.isStrugglingWithConcepts) {
            return ContextStrategy.MISCONCEPTION_FOCUSED
        }

        // Priority 2: Support application-oriented learning
        if (indicators.isInApplicationPhase || bloomLevel === BloomLevel.APPLY) {
            return ContextStrategy.APPLICATION_ORIENTED
        }

        // Priority 3: Support review and reinforcement
        if (indicators.isReviewingPreviousContent || bloomLevel === BloomLevel.REMEMBER) {
            return ContextStrategy.REVIEW_MODE
        }

        // Priority 4: Support discovery and exploration
        if (indicators.isExploringNewTopics || indicators.engagementLevel === 'high') {
            return ContextStrategy.DISCOVERY_MODE
        }

        // Default: Concept building for general learning
        return ContextStrategy.CONCEPT_BUILDING
    }

    /**
     * Analyze question complexity based on linguistic patterns
     * 
     * @param query - User query to analyze
     * @returns Question complexity level
     */
    private analyzeQuestionComplexity(query: string): 'simple' | 'moderate' | 'complex' {
        const lowerQuery = query.toLowerCase()

        // Simple question indicators
        const simpleIndicators = ['what is', 'who is', 'when', 'where', 'define', 'meaning']
        if (simpleIndicators.some(indicator => lowerQuery.includes(indicator))) {
            return 'simple'
        }

        // Complex question indicators
        const complexIndicators = ['analyze', 'compare', 'evaluate', 'synthesize', 'relationship between', 'implications']
        if (complexIndicators.some(indicator => lowerQuery.includes(indicator))) {
            return 'complex'
        }

        // Moderate complexity by default
        return 'moderate'
    }

    /**
     * Analyze engagement level from query characteristics
     * 
     * @param query - User query to analyze
     * @returns Engagement level
     */
    private analyzeEngagementLevel(query: string): 'low' | 'medium' | 'high' {
        const queryLength = query.length
        const questionMarks = (query.match(/\?/g) || []).length
        const exclamationMarks = (query.match(/!/g) || []).length

        // High engagement indicators
        if (queryLength > 100 || questionMarks > 1 || exclamationMarks > 0) {
            return 'high'
        }

        // Low engagement indicators
        if (queryLength < 20 || query.trim().split(' ').length < 4) {
            return 'low'
        }

        return 'medium'
    }

    /**
     * Detect misconception indicators in user query
     * 
     * @param query - User query to analyze
     * @returns True if misconception indicators detected
     */
    private detectMisconceptionIndicators(query: string): boolean {
        const lowerQuery = query.toLowerCase()
        const misconceptionKeywords = [
            'confused', 'don\'t understand', 'doesn\'t make sense', 'wrong',
            'mistake', 'error', 'unclear', 'contradicts', 'opposite'
        ]

        return misconceptionKeywords.some(keyword => lowerQuery.includes(keyword))
    }

    /**
     * Detect struggle indicators in user query
     * 
     * @param query - User query to analyze
     * @returns True if struggle indicators detected
     */
    private detectStruggleIndicators(query: string): boolean {
        const lowerQuery = query.toLowerCase()
        const struggleKeywords = [
            'difficult', 'hard', 'struggling', 'can\'t grasp', 'lost',
            'overwhelmed', 'complex', 'complicated', 'help me understand'
        ]

        return struggleKeywords.some(keyword => lowerQuery.includes(keyword))
    }

    /**
     * Detect application-oriented indicators in user query
     * 
     * @param query - User query to analyze
     * @returns True if application indicators detected
     */
    private detectApplicationIndicators(query: string): boolean {
        const lowerQuery = query.toLowerCase()
        const applicationKeywords = [
            'how to use', 'apply', 'implement', 'practice', 'real world',
            'example', 'scenario', 'case study', 'practical', 'solve'
        ]

        return applicationKeywords.some(keyword => lowerQuery.includes(keyword))
    }

    /**
     * Detect review-oriented indicators in user query
     * 
     * @param query - User query to analyze
     * @returns True if review indicators detected
     */
    private detectReviewIndicators(query: string): boolean {
        const lowerQuery = query.toLowerCase()
        const reviewKeywords = [
            'review', 'summarize', 'recap', 'go over', 'remind me',
            'what did we learn', 'previous', 'earlier', 'before'
        ]

        return reviewKeywords.some(keyword => lowerQuery.includes(keyword))
    }

    /**
     * Detect exploration/discovery indicators in user query
     * 
     * @param query - User query to analyze
     * @returns True if exploration indicators detected
     */
    private detectExplorationIndicators(query: string): boolean {
        const lowerQuery = query.toLowerCase()
        const explorationKeywords = [
            'what if', 'explore', 'discover', 'learn more', 'tell me about',
            'curious', 'interesting', 'related to', 'connection', 'similar'
        ]

        return explorationKeywords.some(keyword => lowerQuery.includes(keyword))
    }
}

// Export singleton instance
export const contextStrategySelector = new ContextStrategySelector()
