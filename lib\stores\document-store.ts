/**
 * Document Store - Manages document state and operations
 * Handles document CRUD, selection, and persistence
 * Max 150 lines - split if needed
 */

import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import type { DocumentStoreState, DocumentActions, Document } from './types'

type DocumentStore = DocumentStoreState & DocumentActions

export const useDocumentStore = create<DocumentStore>()(
  persist(
    (set, get) => ({
      // Initial state
      documents: [],
      activeDocument: null,
      selectedDocument: null,
      loading: false,
      error: null,

      // Actions
      setActiveDocument: (doc) => {
        set({ activeDocument: doc })
        
        // Sync with server in background
        if (doc) {
          syncActiveDocumentWithServer(doc.id).catch(console.warn)
        }
      },

      selectDocument: async (doc: Document | null) => {
        set({ selectedDocument: doc, error: null })

        // Also set as active document if doc is not null
        if (doc) {
          get().setActiveDocument(doc)
        } else {
          // If doc is null, clear the active document
          get().setActiveDocument(null)
        }
      },

      loadDocuments: async () => {
        set({ loading: true, error: null })
        
        try {
          const response = await fetch('/api/documents')
          
          if (!response.ok) {
            throw new Error('Failed to load documents')
          }
          
          const data = await response.json()
          set({ documents: data.documents || [], loading: false })
          
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Failed to load documents',
            loading: false 
          })
        }
      },

      uploadDocument: async (file) => {
        set({ loading: true, error: null })
        
        try {
          const formData = new FormData()
          formData.append('file', file)
          
          const response = await fetch('/api/documents', {
            method: 'POST',
            body: formData
          })
          
          if (!response.ok) {
            throw new Error('Failed to upload document')
          }
          
          const data = await response.json()
          const newDocument = data.document
          
          // Add to documents list
          set(state => ({
            documents: [...state.documents, newDocument],
            loading: false
          }))
          
          // Auto-select the uploaded document
          get().selectDocument(newDocument)
          
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Failed to upload document',
            loading: false 
          })
        }
      },

      deleteDocument: async (id) => {
        set({ loading: true, error: null })
        
        try {
          const response = await fetch(`/api/documents/${id}`, {
            method: 'DELETE'
          })
          
          if (!response.ok) {
            throw new Error('Failed to delete document')
          }
          
          // Remove from state
          set(state => ({
            documents: state.documents.filter(doc => doc.id !== id),
            activeDocument: state.activeDocument?.id === id ? null : state.activeDocument,
            selectedDocument: state.selectedDocument?.id === id ? null : state.selectedDocument,
            loading: false
          }))

          // Clean up related data from other stores
          try {
            const { useChatStore } = await import('./chat-store')
            const { useSessionStore } = await import('./session-store')

            // Clear messages for this document
            useChatStore.getState().clearMessages(id)

            // Clear session for this document
            useSessionStore.getState().clearSession(id)

            // Clear localStorage data for this document
            if (typeof window !== 'undefined') {
              localStorage.removeItem(`guided-tutor-session-${id}`)
              localStorage.removeItem(`guided-tutor-messages-${id}`)

              // Also clear if this was the last selected document
              const lastSelected = localStorage.getItem('lastSelectedDocument')
              if (lastSelected) {
                try {
                  const parsed = JSON.parse(lastSelected)
                  if (parsed.id === id) {
                    localStorage.removeItem('lastSelectedDocument')
                  }
                } catch (e) {
                  // Ignore parse errors
                }
              }
            }

            console.log(`🧹 Cleaned up chat, session, and localStorage data for document: ${id}`)
          } catch (error) {
            console.warn('Failed to clean up related data:', error)
          }

          // Trigger deletion event
          window.dispatchEvent(new CustomEvent('documentDeleted', {
            detail: { documentId: id }
          }))
          
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Failed to delete document',
            loading: false 
          })
        }
      },

      updateDocumentStatus: (id, status) => {
        set(state => ({
          documents: state.documents.map(doc => 
            doc.id === id ? { ...doc, status } : doc
          ),
          activeDocument: state.activeDocument?.id === id 
            ? { ...state.activeDocument, status }
            : state.activeDocument,
          selectedDocument: state.selectedDocument?.id === id
            ? { ...state.selectedDocument, status }
            : state.selectedDocument
        }))
      },

      clearError: () => set({ error: null })
    }),
    {
      name: 'document-store',
      partialize: (state) => ({
        activeDocument: state.activeDocument,
        documents: state.documents
      })
    }
  )
)

// Helper function for server sync
async function syncActiveDocumentWithServer(documentId: string) {
  try {
    await fetch('/api/user/active-document', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ documentId })
    })
  } catch (error) {
    console.warn('Failed to sync active document with server:', error)
  }
}