"use client"

import { useState, useEffect, useRef, useCallback } from "react"
import { CompactUpload } from "@/components/dashboard/compact-upload"
import { useSidebar } from "@/components/ui/sidebar"
import { useSidebarContext } from "@/components/sidebar-context"
import { But<PERSON> } from "@/components/ui/button"
import { Loader2, ArrowUp, PanelLeft } from "lucide-react"
import { AIThinking } from "@/components/ui/ai-thinking"
import { useDocumentStore } from "@/lib/stores/document-store"
import { useChatStore } from "@/lib/stores/chat-store"
import { useSessionStore } from "@/lib/stores/session-store"
import { toast } from "sonner"
import { ConnectionStatus } from "@/components/ui/connection-status"
import { OfflineBanner } from "@/components/ui/offline-banner"

export default function DashboardPage() {
  // Zustand store subscriptions
  const {
    selectedDocument,
    selectDocument,
    loadDocuments
  } = useDocumentStore()

  const {
    messages,
    loading: chatLoading,
    initializing: chatInitializing,
    addMessage,
    updateMessage,
    removeMessage,
    initializeChat,
    sendMessage: chatSendMessage,
    triggerAutoResponse
  } = useChatStore()

  const {
    currentSession,
    initializeSession,
    updateProgress
  } = useSessionStore()

  // Local UI state (not managed by stores)
  const [input, setInput] = useState("")
  const [loading, setLoading] = useState(false)

  // UI refs
  const { setOpenMobile } = useSidebar()
  const { sidebarVisible, setSidebarVisible } = useSidebarContext()
  const textareaRef = useRef<HTMLTextAreaElement>(null)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const messagesContainerRef = useRef<HTMLDivElement>(null)

  // Get current document messages from store
  const currentMessages = selectedDocument ? messages[selectedDocument.id] || [] : []

  // Get current chunk info from session
  const currentChunk = currentSession?.currentChunk || 0
  const totalChunks = currentSession?.totalChunks || 0

  // Note: triggerAIResponse moved to chat store as triggerAutoResponse

  // Handle next chunk navigation
  const handleNextChunk = useCallback(async () => {
    if (!selectedDocument || currentChunk >= totalChunks - 1) return

    setLoading(true)
    try {
      console.log(`🔄 Loading next chunk from current: ${currentChunk} to ${currentChunk + 1}`)

      // Step 1: Get the next chunk content first
      const chunkResponse = await fetch(`/api/documents/${selectedDocument.id}/chunks/${currentChunk + 1}`)
      if (!chunkResponse.ok) {
        throw new Error("Failed to fetch next chunk content")
      }

      const chunkData = await chunkResponse.json()

      // Step 2: Display the chunk content immediately
      const chunkMessage = {
        id: `chunk_${currentChunk + 1}_${Date.now()}`,
        role: "user" as const,
        content: chunkData.content,
        timestamp: new Date().toISOString(),
        documentId: selectedDocument.id
      }

      addMessage(chunkMessage)
      console.log('✅ Next chunk content displayed')

      // Step 3: Add AI thinking indicator
      const thinkingMessage = {
        id: `thinking_${Date.now()}`,
        role: "assistant" as const,
        content: "",
        timestamp: new Date().toISOString(),
        documentId: selectedDocument.id,
        isThinking: true
      }

      addMessage(thinkingMessage)
      console.log('🤔 AI thinking indicator added for next chunk')

      // Step 4: Update progress
      updateProgress(selectedDocument.id, currentChunk + 1)

      // Step 5: Stream AI response for the new chunk
      const streamResponse = await fetch("/api/chat/stream", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          documentId: selectedDocument.id,
          message: `Continue learning with this new section: ${chunkData.content}`,
          chunkIndex: currentChunk + 1,
          bloomLevel: 'understand' // New section introduction level
        }),
      })

      if (!streamResponse.ok) {
        throw new Error(`Failed to get AI response: ${streamResponse.status}`)
      }

      // Step 6: Handle streaming response
      const reader = streamResponse.body?.getReader()
      if (!reader) throw new Error("No response body")

      const aiMessage = {
        id: `ai_chunk_${currentChunk + 1}_${Date.now()}`,
        role: "assistant" as const,
        content: "",
        timestamp: new Date().toISOString(),
        documentId: selectedDocument.id
      }

      // Replace thinking message with AI response
      removeMessage(thinkingMessage.id)
      addMessage(aiMessage)

      // Stream the response
      while (true) {
        const { done, value } = await reader.read()
        if (done) break

        const chunk = new TextDecoder().decode(value)
        const lines = chunk.split('\n')

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6))
              if (data.type === 'token' && data.content) {
                updateMessage(aiMessage.id, {
                  content: (currentMessages.find(m => m.id === aiMessage.id)?.content || '') + data.content
                })
              }
            } catch (e) {
              console.warn('Parse error:', e)
            }
          }
        }
      }

      console.log(`✅ Successfully loaded chunk ${currentChunk + 1} with streaming response`)
      toast.success(`Moved to section ${currentChunk + 2} of ${totalChunks}`)

    } catch (error) {
      console.error("Failed to load next chunk:", error)

      // Remove thinking message on error
      const thinkingMsg = currentMessages.find(msg => msg.isThinking)
      if (thinkingMsg) {
        removeMessage(thinkingMsg.id)
      }

      toast.error(error instanceof Error ? error.message : "Failed to load next section")
    } finally {
      setLoading(false)
    }
  }, [selectedDocument, currentChunk, totalChunks, updateProgress, addMessage, removeMessage, updateMessage, currentMessages])

  // Handle form submission
  const handleSubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault()
    if (!input.trim() || chatLoading || !selectedDocument) return

    const userMessage = input.trim()
    setInput("")

    // Send message using Zustand store action (it will handle adding the user message)
    try {
      await chatSendMessage(userMessage)
    } catch (error) {
      console.error('Failed to send message:', error)
      toast.error('Failed to send message. Please try again.')
    }
  }, [input, chatLoading, selectedDocument, chatSendMessage])

  // Handle navigation to upload
  const handleNavigateToUpload = useCallback(async () => {
    await selectDocument(null)
    setOpenMobile(false)
  }, [selectDocument, setOpenMobile])

  // Handle document deletion
  const handleDocumentDeleted = useCallback(async (event: Event) => {
    const customEvent = event as CustomEvent<{ documentId: string }>
    const deletedDocumentId = customEvent.detail.documentId

    // If the currently selected document was deleted, clear the selection
    if (selectedDocument && selectedDocument.id === deletedDocumentId) {
      console.log('🗑️ Currently active document was deleted, redirecting to upload interface')
      await selectDocument(null)
      toast.info('Document deleted. Upload a new document to continue learning.')
    }
  }, [selectedDocument, selectDocument])

  // Auto-scroll to bottom when messages change
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: "smooth" })
    }
  }, [currentMessages])

  // Note: handleDocumentSelect removed - now using direct Zustand store calls from document list

  // Handle Enter key submission
  const handleKeyDown = useCallback((e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSubmit(e as any)
    }
  }, [handleSubmit])

  // Note: Event listener removed - now using direct Zustand store calls

  // Event listeners for navigation and document deletion
  useEffect(() => {
    window.addEventListener("navigateToUpload", handleNavigateToUpload as EventListener)
    window.addEventListener("documentDeleted", handleDocumentDeleted as EventListener)

    return () => {
      window.removeEventListener("navigateToUpload", handleNavigateToUpload as EventListener)
      window.removeEventListener("documentDeleted", handleDocumentDeleted as EventListener)
    }
  }, [handleNavigateToUpload, handleDocumentDeleted])

  // Auto-restore functionality - automatically select last active document on mount
  useEffect(() => {
    const restoreActiveDocument = async () => {
      // Only restore if no document is currently selected
      if (selectedDocument) return

      try {
        // First try to get active document from server
        const response = await fetch('/api/user/active-document')
        if (response.ok) {
          const data = await response.json()
          if (data.hasActiveDocument) {
            const documentInfo = {
              id: data.document.id,
              name: data.document.name,
              fileName: data.document.name,
              status: 'READY' as const,
              totalChunks: 0,
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString()
            }

            console.log('🔄 Auto-restoring active document:', documentInfo.name)
            await selectDocument(documentInfo)
            await initializeSession(documentInfo.id)
            await initializeChat(documentInfo.id)

            // Initialize AI session before triggering auto response
            console.log('🤖 Initializing AI session for restored document...')
            const aiInitResponse = await fetch('/api/ai/initialize', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ documentId: documentInfo.id })
            })

            if (aiInitResponse.ok) {
              console.log('✅ AI session initialized for restored document')
              await triggerAutoResponse(documentInfo.id, "Let's continue learning about this document.")
            } else {
              console.warn('⚠️ AI initialization failed for restored document, skipping auto response')
            }
            return
          }
        }

        // Fallback to localStorage
        const stored = localStorage.getItem('lastSelectedDocument')
        if (stored) {
          const documentInfo = JSON.parse(stored)

          // Verify document still exists
          const verifyResponse = await fetch(`/api/documents/${documentInfo.id}`)
          if (verifyResponse.ok) {
            const fullDocumentInfo = {
              id: documentInfo.id,
              name: documentInfo.name,
              fileName: documentInfo.name,
              status: 'READY' as const,
              totalChunks: 0,
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString()
            }

            console.log('🔄 Auto-restoring document from localStorage:', documentInfo.name)
            await selectDocument(fullDocumentInfo)
            await initializeSession(documentInfo.id)
            await initializeChat(documentInfo.id)
            await triggerAutoResponse(documentInfo.id, "Let's continue learning about this document.")
          } else {
            // Document no longer exists, clear localStorage
            localStorage.removeItem('lastSelectedDocument')
          }
        }
      } catch (error) {
        console.warn('Failed to restore active document:', error)
      }
    }

    restoreActiveDocument()
  }, []) // Empty dependency array - only run on mount

  // Auto-resize textarea when input changes
  useEffect(() => {
    if (textareaRef.current) {
      const textarea = textareaRef.current
      textarea.style.height = 'auto'
      textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px'
    }
  }, [input])

  // Handle upload completion
  const handleUploadComplete = useCallback(async (documentId: string, filename: string) => {
    console.log('📄 Upload completed:', { documentId, filename })

    // Prevent multiple simultaneous uploads
    if (chatLoading) {
      console.log('⏭️ Skipping upload completion - chat is loading')
      return
    }

    try {
      // Refresh the document list to get the latest data
      await loadDocuments()

      // Fetch the actual document data from the server
      const response = await fetch(`/api/documents/${documentId}`)
      if (!response.ok) {
        throw new Error('Failed to fetch document details')
      }

      const data = await response.json()
      const newDocument = data.document

      // Select the newly uploaded document
      await selectDocument(newDocument)

      // Initialize session for the new document
      await initializeSession(documentId)

      // Initialize chat for the new document
      await initializeChat(documentId)

      // Initialize AI session before triggering auto response
      console.log('🤖 Initializing AI session for new document...')
      const aiInitResponse = await fetch('/api/ai/initialize', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ documentId })
      })

      if (aiInitResponse.ok) {
        console.log('✅ AI session initialized for new document')
        // Trigger AI response for first chunk
        await triggerAutoResponse(documentId, "")
      } else {
        console.warn('⚠️ AI initialization failed for new document, skipping auto response')
      }

      toast.success(`Document "${filename}" uploaded successfully!`)
      console.log('✅ Document upload and initialization complete')
    } catch (error) {
      console.error('❌ Failed to handle upload completion:', error)
      toast.error('Failed to initialize uploaded document')
    }
  }, [selectDocument, initializeSession, initializeChat, triggerAutoResponse, chatLoading, loadDocuments])



  return (
    <div className="h-full flex flex-col">
      {/* Offline Banner */}
      <OfflineBanner />

      {/* Connection Status Indicator */}
      <ConnectionStatus />

      {/* Fixed Navbar - Using unified color #F9FAFB */}
      <div className="bg-[#F9FAFB] px-3 py-1 flex-shrink-0 flex items-center min-h-[48px]">
        <Button
          variant="ghost"
          size="icon"
          onClick={() => setSidebarVisible(!sidebarVisible)}
          className="h-8 w-8 p-1 hover:bg-gray-200"
        >
          <PanelLeft className="h-4 w-4" />
        </Button>
        {selectedDocument && (
          <div className="px-6 py-4 flex items-center justify-end">
            <div className="flex items-center gap-4">
              <div className="text-xs text-gray-600 font-body">
                Section {currentChunk + 1} of {totalChunks}
              </div>
              <Button
                onClick={handleNextChunk}
                disabled={currentChunk >= totalChunks - 1 || loading}
                className="bg-primary hover:bg-primary/90 text-white px-3 py-1 text-xs font-body disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? (
                  <>
                    <Loader2 className="w-3 h-3 animate-spin mr-1" />
                    Loading...
                  </>
                ) : currentChunk >= totalChunks - 1 ? (
                  "Completed"
                ) : (
                  `Next Section →`
                )}
              </Button>
            </div>
          </div>
        )}
      </div>

      {/* Main Content Area */}
      {selectedDocument ? (
        // Chat interface
        <div className="flex-1 flex flex-col bg-white overflow-hidden">
          {/* Messages Area - PROPERLY Scrollable */}
          <div
            ref={messagesContainerRef}
            className="flex-1 overflow-y-auto pb-32 hide-scrollbar"
            style={{
              height: 'calc(100vh - 120px)',
              scrollBehavior: 'smooth'
            }}
          >
            {chatInitializing ? (
              <div className="flex items-center justify-center h-full">
                <div className="text-center">
                  <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-primary" />
                  <p className="text-gray-600 font-body">Starting your study session...</p>
                </div>
              </div>
            ) : (
              <div className="w-full px-6 py-6">
                {currentMessages.length === 0 && (
                  <div className="text-center py-12">
                    <p className="text-gray-600 font-body text-lg">
                      Start a conversation about this document...
                    </p>
                  </div>
                )}
                {/* Match the input box width constraint */}
                <div className="max-w-2xl mx-auto space-y-6">
                  {currentMessages.map((message) => (
                    <div key={message.id}>
                      {message.role === "user" ? (
                        // User message - Right aligned within the container
                        <div className="flex justify-end w-full pr-4">
                          <div className="max-w-[85%] bg-gray-100 text-gray-900 rounded-2xl px-4 py-3 font-body">
                            <p className="whitespace-pre-wrap leading-relaxed">{message.content}</p>
                          </div>
                        </div>
                      ) : (
                        // AI message - Left aligned but with proper spacing to match input field
                        <div className="flex justify-start w-full">
                          <div className="w-full max-w-full text-gray-900 font-body pl-4">
                            {message.isThinking ? (
                              <AIThinking />
                            ) : (
                              <p className="whitespace-pre-wrap leading-relaxed">{message.content}</p>
                            )}
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                  {/* Scroll anchor */}
                  <div ref={messagesEndRef} />

                </div>
              </div>
            )}
          </div>
        </div>
      ) : (
        // Upload interface
        <div className="flex-1 flex items-center justify-center bg-white">
          <div className="text-center max-w-md">
            <div className="mb-8">
              <h1 className="text-2xl font-semibold text-gray-900 mb-2 font-heading">Welcome to Guided Tutor</h1>
              <p className="text-gray-600 font-body">Upload your first document to start learning</p>
            </div>
            <CompactUpload onUploadComplete={handleUploadComplete} />
          </div>
        </div>
      )}

      {/* Fixed Input Area - Only show when document is selected */}
      {selectedDocument && (
        <div
          className="fixed bottom-0 bg-white z-10"
          style={{
            left: sidebarVisible ? '260px' : '0px',
            right: '0px',
            width: sidebarVisible ? 'calc(100vw - 260px)' : '100vw',
            padding: '1.5rem',
            margin: '0',
            boxSizing: 'border-box'
          }}
        >
          <div className="max-w-2xl mx-auto">
            <div className="relative flex items-end bg-gray-50 border border-gray-300 rounded-lg px-4 py-3 focus-within:border-gray-400">
              <textarea
                ref={textareaRef}
                value={input}
                onChange={(e) => setInput(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder="Teach me about..."
                disabled={loading}
                rows={1}
                className="flex-1 bg-transparent border-0 px-0 py-0 font-body text-gray-900 placeholder-gray-500 focus:ring-0 focus:outline-none focus-visible:ring-0 focus-visible:outline-none resize-none overflow-y-auto"
                style={{
                  minHeight: '24px',
                  maxHeight: '120px',
                  lineHeight: '24px'
                }}
                onInput={(e) => {
                  const target = e.target as HTMLTextAreaElement
                  target.style.height = 'auto'
                  target.style.height = Math.min(target.scrollHeight, 120) + 'px'
                }}
              />
              <Button
                onClick={handleSubmit}
                disabled={loading || !input.trim()}
                size="icon"
                className="ml-2 bg-primary hover:bg-primary/90 text-white rounded-full h-8 w-8 flex-shrink-0"
              >
                <ArrowUp className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
