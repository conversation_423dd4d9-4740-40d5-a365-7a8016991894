import { NextRequest, NextResponse } from 'next/server'
import { memoryService } from '@/lib/ai/memory-service'

export async function GET(_request: NextRequest) {
    try {
        console.log('🧪 Simple Mem0 test...')

        // Test document and user
        const testDocumentId = `simple-test-doc-${Date.now()}`
        const testUserId = `simple-test-user-${Date.now()}`

        // Add a simple memory
        console.log('📝 Adding simple memory...')
        await memoryService.addConversationMessage(testDocumentId, testUserId, 'I love pizza', 'user')

        // Skip search for now since memories are async and may not be immediately available
        console.log('⏭️ Skipping search test - memories are processing asynchronously')
        const results: never[] = []

        return NextResponse.json({
            success: true,
            testDocumentId,
            testUserId,
            searchResults: results,
            message: 'Simple test completed'
        })

    } catch (error) {
        console.error('❌ Simple test failed:', error)
        return NextResponse.json({
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error'
        }, { status: 500 })
    }
}
