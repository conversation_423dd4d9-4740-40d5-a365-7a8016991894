/**
 * Stream Buffer
 * Provides client-side buffering for smoother streaming responses
 */

export interface BufferConfig {
    bufferSize: number // Number of tokens to buffer
    flushInterval: number // Milliseconds between flushes
    minFlushSize: number // Minimum tokens before flushing
    maxWaitTime: number // Maximum time to wait before flushing
    enableSmoothing: boolean // Enable smooth token delivery
}

export interface BufferedToken {
    content: string
    timestamp: number
    sequence: number
}

export class StreamBuffer {
    private buffer: BufferedToken[] = []
    private flushTimer: NodeJS.Timeout | null = null
    private sequenceCounter = 0
    protected config: BufferConfig
    private onFlush: (tokens: BufferedToken[]) => void
    private isActive = false

    constructor(
        onFlush: (tokens: BufferedToken[]) => void,
        config?: Partial<BufferConfig>
    ) {
        this.onFlush = onFlush
        this.config = {
            bufferSize: 5, // Buffer 5 tokens at a time
            flushInterval: 50, // Flush every 50ms
            minFlushSize: 1, // Flush at least 1 token
            maxWaitTime: 200, // Max 200ms wait
            enableSmoothing: true,
            ...config
        }
    }

    /**
     * Add token to buffer
     */
    addToken(content: string): void {
        if (!this.config.enableSmoothing) {
            // If smoothing disabled, flush immediately
            this.onFlush([{
                content,
                timestamp: Date.now(),
                sequence: ++this.sequenceCounter
            }])
            return
        }

        const token: BufferedToken = {
            content,
            timestamp: Date.now(),
            sequence: ++this.sequenceCounter
        }

        this.buffer.push(token)
        this.scheduleFlush()
    }

    /**
     * Start buffering
     */
    start(): void {
        this.isActive = true
        console.log('🚀 Stream buffer started')
    }

    /**
     * Stop buffering and flush remaining tokens
     */
    async stop(): Promise<void> {
        this.isActive = false
        
        if (this.flushTimer) {
            clearTimeout(this.flushTimer)
            this.flushTimer = null
        }

        // Flush any remaining tokens
        if (this.buffer.length > 0) {
            await this.flush()
        }

        console.log('⏹️ Stream buffer stopped')
    }

    /**
     * Schedule buffer flush
     */
    private scheduleFlush(): void {
        if (!this.isActive) return

        // Flush immediately if buffer is full
        if (this.buffer.length >= this.config.bufferSize) {
            this.flush()
            return
        }

        // Schedule flush if not already scheduled
        if (!this.flushTimer) {
            this.flushTimer = setTimeout(() => {
                this.flush()
            }, this.config.flushInterval)
        }

        // Check for tokens that have waited too long
        const now = Date.now()
        const hasOldTokens = this.buffer.some(
            token => now - token.timestamp > this.config.maxWaitTime
        )

        if (hasOldTokens) {
            this.flush()
        }
    }

    /**
     * Flush buffered tokens
     */
    protected async flush(): Promise<void> {
        if (this.buffer.length === 0) {
            return
        }

        // Clear the timer
        if (this.flushTimer) {
            clearTimeout(this.flushTimer)
            this.flushTimer = null
        }

        // Determine how many tokens to flush
        const flushSize = Math.max(
            this.config.minFlushSize,
            Math.min(this.buffer.length, this.config.bufferSize)
        )

        const tokensToFlush = this.buffer.splice(0, flushSize)
        
        if (tokensToFlush.length > 0) {
            this.onFlush(tokensToFlush)
        }

        // Schedule next flush if there are remaining tokens
        if (this.buffer.length > 0 && this.isActive) {
            this.scheduleFlush()
        }
    }

    /**
     * Force flush all buffered tokens
     */
    async forceFlush(): Promise<void> {
        while (this.buffer.length > 0) {
            await this.flush()
        }
    }

    /**
     * Get buffer statistics
     */
    getStats(): {
        bufferedTokens: number
        totalProcessed: number
        isActive: boolean
        isFlushScheduled: boolean
        averageWaitTime: number
    } {
        const now = Date.now()
        const averageWaitTime = this.buffer.length > 0
            ? this.buffer.reduce((sum, token) => sum + (now - token.timestamp), 0) / this.buffer.length
            : 0

        return {
            bufferedTokens: this.buffer.length,
            totalProcessed: this.sequenceCounter,
            isActive: this.isActive,
            isFlushScheduled: this.flushTimer !== null,
            averageWaitTime
        }
    }

    /**
     * Update buffer configuration
     */
    updateConfig(newConfig: Partial<BufferConfig>): void {
        this.config = { ...this.config, ...newConfig }
        console.log('📝 Updated stream buffer configuration:', this.config)
    }

    /**
     * Clear buffer without flushing
     */
    clear(): void {
        this.buffer = []
        
        if (this.flushTimer) {
            clearTimeout(this.flushTimer)
            this.flushTimer = null
        }
        
        console.log('🗑️ Stream buffer cleared')
    }
}

/**
 * Adaptive Stream Buffer
 * Automatically adjusts buffer settings based on network conditions
 */
export class AdaptiveStreamBuffer extends StreamBuffer {
    private latencyHistory: number[] = []
    private adaptationEnabled = true

    constructor(
        onFlush: (tokens: BufferedToken[]) => void,
        config?: Partial<BufferConfig>
    ) {
        super(onFlush, config)
    }

    /**
     * Override flush to measure latency and adapt
     */
    protected async flush(): Promise<void> {
        const startTime = Date.now()
        
        await super.flush()
        
        const flushLatency = Date.now() - startTime
        this.recordLatency(flushLatency)
        
        if (this.adaptationEnabled) {
            this.adaptBufferSettings()
        }
    }

    /**
     * Record flush latency for adaptation
     */
    private recordLatency(latency: number): void {
        this.latencyHistory.push(latency)
        
        // Keep only last 20 measurements
        if (this.latencyHistory.length > 20) {
            this.latencyHistory.shift()
        }
    }

    /**
     * Adapt buffer settings based on performance
     */
    private adaptBufferSettings(): void {
        if (this.latencyHistory.length < 5) {
            return // Need more data
        }

        const averageLatency = this.latencyHistory.reduce((a, b) => a + b, 0) / this.latencyHistory.length
        const currentConfig = { ...this.config }

        // High latency: increase buffer size, decrease flush frequency
        if (averageLatency > 100) {
            this.config.bufferSize = Math.min(10, this.config.bufferSize + 1)
            this.config.flushInterval = Math.min(200, this.config.flushInterval + 10)
        }
        // Low latency: decrease buffer size, increase flush frequency
        else if (averageLatency < 20) {
            this.config.bufferSize = Math.max(2, this.config.bufferSize - 1)
            this.config.flushInterval = Math.max(20, this.config.flushInterval - 5)
        }

        // Log adaptation if settings changed
        if (JSON.stringify(currentConfig) !== JSON.stringify(this.config)) {
            console.log(`🔧 Adapted buffer settings based on ${averageLatency.toFixed(1)}ms latency:`, {
                bufferSize: this.config.bufferSize,
                flushInterval: this.config.flushInterval
            })
        }
    }

    /**
     * Enable/disable automatic adaptation
     */
    setAdaptationEnabled(enabled: boolean): void {
        this.adaptationEnabled = enabled
        console.log(`🔧 Buffer adaptation ${enabled ? 'enabled' : 'disabled'}`)
    }

    /**
     * Get adaptation statistics
     */
    getAdaptationStats(): {
        averageLatency: number
        latencyHistory: number[]
        adaptationEnabled: boolean
        adaptationCount: number
    } {
        const averageLatency = this.latencyHistory.length > 0
            ? this.latencyHistory.reduce((a, b) => a + b, 0) / this.latencyHistory.length
            : 0

        return {
            averageLatency,
            latencyHistory: [...this.latencyHistory],
            adaptationEnabled: this.adaptationEnabled,
            adaptationCount: this.latencyHistory.length
        }
    }
}

// Export both classes
export { StreamBuffer as default }
