/**
 * Chat Store - Manages chat messages and AI interactions
 * Handles message CRUD, streaming, and AI responses
 * Max 150 lines - split if needed
 */

import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import type { Message, ChatStoreState, ChatActions } from './types'

type ChatStore = ChatStoreState & ChatActions & {
  handleAIInitialization: (documentId: string, originalMessage: string) => Promise<void>
}

export const useChatStore = create<ChatStore>()(
  persist(
    (set, get) => ({
      // Initial state
      messages: {},
      currentDocumentId: null,
      streaming: false,
      loading: false,
      initializing: false,
      error: null,

      // Actions
      initializeChat: async (documentId) => {
        set({ currentDocumentId: documentId, initializing: true, error: null })
        
        try {
          const response = await fetch('/api/chat/initialize', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ documentId })
          })
          
          if (!response.ok) {
            const errorData = await response.json()
            throw new Error(errorData.error || 'Failed to initialize chat')
          }
          
          const data = await response.json()
          
          // Set initial messages if provided
          if (data.messages && data.messages.length > 0) {
            set(state => ({
              messages: {
                ...state.messages,
                [documentId]: data.messages
              },
              initializing: false
            }))
          } else {
            set({ initializing: false })
          }
          
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Failed to initialize chat',
            initializing: false
          })
        }
      },

      sendMessage: async (content) => {
        const { currentDocumentId } = get()
        if (!currentDocumentId) return

        set({ loading: true })

        try {
          // Create optimistic user message
          const userMessage: Message = {
            id: `user_${Date.now()}`,
            role: 'user',
            content,
            timestamp: new Date().toISOString(),
            documentId: currentDocumentId
          }

          // Add user message immediately
          get().addMessage(userMessage)

          // Add thinking message
          const thinkingMessage: Message = {
            id: `thinking_${Date.now()}`,
            role: 'assistant',
            content: '',
            timestamp: new Date().toISOString(),
            documentId: currentDocumentId,
            isThinking: true
          }

          get().addMessage(thinkingMessage)

          // Get current chunk from session store (import at runtime to avoid circular deps)
          let currentChunk = 0
          let sessionInitialized = false
          try {
            const { useSessionStore } = await import('./session-store')
            const sessionState = useSessionStore.getState()
            const session = sessionState.sessions[currentDocumentId]
            currentChunk = session?.currentChunk || 0
            sessionInitialized = session?.initialized || false
          } catch (error) {
            console.warn('Failed to get current chunk from session store:', error)
          }

          // Ensure session is initialized before sending to stream API
          if (!sessionInitialized) {
            console.log('🔄 Session not initialized, initializing first...')
            await get().initializeChat(currentDocumentId)
          }

          // Send to streaming API
          const response = await fetch('/api/chat/stream', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              documentId: currentDocumentId,
              message: content,
              chunkIndex: currentChunk,
              bloomLevel: 'understand' // Default bloom level for user questions
            })
          })

          if (!response.ok) {
            throw new Error(`Failed to send message: ${response.status}`)
          }

          // Handle streaming response
          const reader = response.body?.getReader()
          if (!reader) throw new Error("No response body")

          const aiMessage: Message = {
            id: `ai_${Date.now()}`,
            role: 'assistant',
            content: '',
            timestamp: new Date().toISOString(),
            documentId: currentDocumentId
          }

          // Replace thinking message with AI response
          get().removeMessage(thinkingMessage.id)
          get().addMessage(aiMessage)

          // Stream the response
          while (true) {
            const { done, value } = await reader.read()
            if (done) break

            const chunk = new TextDecoder().decode(value)
            const lines = chunk.split('\n')

            for (const line of lines) {
              if (line.startsWith('data: ')) {
                try {
                  const data = JSON.parse(line.slice(6))
                  if (data.type === 'token' && data.content) {
                    const currentMessages = get().messages[currentDocumentId] || []
                    const currentAiMessage = currentMessages.find(m => m.id === aiMessage.id)
                    const currentContent = currentAiMessage?.content || ''

                    get().updateMessage(aiMessage.id, {
                      content: currentContent + data.content
                    })
                  }
                } catch (e) {
                  console.warn('Parse error:', e)
                }
              }
            }
          }

        } catch (error) {
          console.error('Failed to send message:', error)

          // Remove thinking message on error
          const currentMessages = get().messages[currentDocumentId] || []
          const thinkingMsg = currentMessages.find(msg => msg.isThinking)
          if (thinkingMsg) {
            get().removeMessage(thinkingMsg.id)
          }

          // Add error message
          const errorMessage: Message = {
            id: `error_${Date.now()}`,
            role: 'assistant',
            content: 'Sorry, I encountered an error. Please try again.',
            timestamp: new Date().toISOString(),
            documentId: currentDocumentId
          }
          get().addMessage(errorMessage)

          set({
            error: error instanceof Error ? error.message : 'Failed to send message'
          })
        } finally {
          set({ loading: false })
        }
      },

      addMessage: (message) => {
        set(state => ({
          messages: {
            ...state.messages,
            [message.documentId]: [
              ...(state.messages[message.documentId] || []),
              message
            ]
          }
        }))
      },

      updateMessage: (id, updates) => {
        set(state => {
          const newMessages = { ...state.messages }
          
          for (const documentId in newMessages) {
            newMessages[documentId] = newMessages[documentId].map(msg =>
              msg.id === id ? { ...msg, ...updates } : msg
            )
          }
          
          return { messages: newMessages }
        })
      },

      removeMessage: (id) => {
        set(state => {
          const newMessages = { ...state.messages }
          
          for (const documentId in newMessages) {
            newMessages[documentId] = newMessages[documentId].filter(msg => msg.id !== id)
          }
          
          return { messages: newMessages }
        })
      },

      clearMessages: (documentId) => {
        if (documentId) {
          set(state => ({
            messages: {
              ...state.messages,
              [documentId]: []
            }
          }))
        } else {
          set({ messages: {} })
        }
      },

      setStreaming: (streaming) => set({ streaming }),

      triggerAutoResponse: async (documentId, chunkContent) => {
        console.log('🤖 Starting auto AI response for first chunk')

        try {
          // Step 1: Get the first chunk content if not provided
          let actualChunkContent = chunkContent
          if (!actualChunkContent) {
            const response = await fetch(`/api/documents/${documentId}/chunks/0`)
            if (response.ok) {
              const data = await response.json()
              actualChunkContent = data.content
            }
          }

          // Step 2: Display the first chunk as user message (if we have content)
          if (actualChunkContent) {
            const chunkMessage = {
              id: `chunk_${Date.now()}`,
              role: "user" as const,
              content: actualChunkContent,
              timestamp: new Date().toISOString(),
              documentId
            }

            get().addMessage(chunkMessage)
            console.log('✅ First chunk displayed')
          }

          // Step 3: Add AI thinking indicator immediately
          const thinkingMessage = {
            id: `thinking_${Date.now()}`,
            role: "assistant" as const,
            content: "",
            timestamp: new Date().toISOString(),
            documentId,
            isThinking: true
          }

          get().addMessage(thinkingMessage)
          console.log('🤔 AI thinking indicator added')

          // Step 4: Send to AI via streaming
          const streamResponse = await fetch("/api/chat/stream", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
              documentId,
              message: actualChunkContent || "Let's start learning about this document.",
              chunkIndex: 0,
              bloomLevel: 'understand' // Initial learning level
            }),
          })

          if (!streamResponse.ok) {
            throw new Error(`Failed to get AI response: ${streamResponse.status}`)
          }

          // Step 5: Handle streaming response
          const reader = streamResponse.body?.getReader()
          if (!reader) throw new Error("No response body")

          const aiMessage = {
            id: `ai_${Date.now()}`,
            role: "assistant" as const,
            content: "",
            timestamp: new Date().toISOString(),
            documentId
          }

          // Replace thinking message with AI response
          get().removeMessage(thinkingMessage.id)
          get().addMessage(aiMessage)

          // Stream the response
          while (true) {
            const { done, value } = await reader.read()
            if (done) break

            const chunk = new TextDecoder().decode(value)
            const lines = chunk.split('\n')

            for (const line of lines) {
              if (line.startsWith('data: ')) {
                try {
                  const data = JSON.parse(line.slice(6))
                  if (data.type === 'token' && data.content) {
                    const currentMessages = get().messages[documentId] || []
                    const currentAiMessage = currentMessages.find(m => m.id === aiMessage.id)
                    const currentContent = currentAiMessage?.content || ''

                    get().updateMessage(aiMessage.id, {
                      content: currentContent + data.content
                    })
                  }
                } catch (e) {
                  console.warn('Parse error:', e)
                }
              }
            }
          }

        } catch (error) {
          console.error("Failed to trigger auto response:", error)

          // Remove thinking message and show error
          const currentMessages = get().messages[documentId] || []
          const thinkingMsg = currentMessages.find(msg => msg.isThinking)
          if (thinkingMsg) {
            get().removeMessage(thinkingMsg.id)
          }

          const errorMessage = {
            id: `error_${Date.now()}`,
            role: "assistant" as const,
            content: "I'm having trouble processing this content. Please try asking me a question.",
            timestamp: new Date().toISOString(),
            documentId
          }
          get().addMessage(errorMessage)
        }
      },

      // Helper method for AI initialization
      handleAIInitialization: async (documentId: string, originalMessage: string) => {
        try {
          // Initialize AI first
          const aiResponse = await fetch('/api/ai/initialize', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ documentId })
          })

          if (aiResponse.ok) {
            // Retry original message directly without going through sendMessage to avoid recursion
            const response = await fetch('/api/chat/message', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                documentId,
                message: originalMessage,
                chunkIndex: 0
              })
            })

            if (response.ok) {
              const data = await response.json()

              // Find and update the thinking message
              const state = get()
              const messages = state.messages[documentId] || []
              const thinkingMessage = messages.find(msg => msg.isThinking)

              if (thinkingMessage) {
                get().updateMessage(thinkingMessage.id, {
                  content: data.response,
                  isThinking: false
                })
              }
            } else {
              throw new Error('Failed to send message after initialization')
            }
          } else {
            throw new Error('Failed to initialize AI')
          }
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Failed to initialize AI',
            initializing: false
          })
        } finally {
          set({ initializing: false })
        }
      }
    }),
    {
      name: 'chat-store',
      partialize: (state) => ({
        messages: state.messages
      })
    }
  )
)