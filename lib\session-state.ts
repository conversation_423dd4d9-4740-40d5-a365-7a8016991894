/**
 * Session State Management Utility
 * Prevents unnecessary session reinitializations and tracks session state
 */

interface SessionState {
  documentId: string
  sessionId: string | null
  initialized: boolean
  hasMessages: boolean
  currentChunk: number
  totalChunks: number
  lastAccessed: number
}

class SessionStateManager {
  private sessions: Map<string, SessionState> = new Map()
  private readonly SESSION_TIMEOUT = 30 * 60 * 1000 // 30 minutes
  private readonly STORAGE_KEY_PREFIX = 'guided-tutor-session-'
  private readonly MESSAGES_KEY_PREFIX = 'guided-tutor-messages-'

  /**
   * Check if a session is already initialized and valid
   */
  isSessionInitialized(documentId: string): boolean {
    // First check in-memory cache
    let session = this.sessions.get(documentId)

    // If not in memory, try to load from localStorage
    if (!session) {
      const loadedSession = this.loadSessionFromStorage(documentId)
      if (loadedSession) {
        session = loadedSession
        this.sessions.set(documentId, session)
      }
    }

    if (!session) return false

    // Check if session has expired
    const now = Date.now()
    if (now - session.lastAccessed > this.SESSION_TIMEOUT) {
      this.sessions.delete(documentId)
      this.clearSessionFromStorage(documentId)
      return false
    }

    // Session is valid if it's initialized (sessionId can be null for some valid sessions)
    return session.initialized
  }

  /**
   * Get session state for a document
   */
  getSessionState(documentId: string): SessionState | null {
    const session = this.sessions.get(documentId)
    if (!session) return null

    // Check if session has expired
    const now = Date.now()
    if (now - session.lastAccessed > this.SESSION_TIMEOUT) {
      this.sessions.delete(documentId)
      return null
    }

    // Update last accessed time
    session.lastAccessed = now
    return session
  }

  /**
   * Update session state
   */
  updateSessionState(documentId: string, updates: Partial<SessionState>): void {
    const existing = this.sessions.get(documentId)
    const now = Date.now()

    const sessionState: SessionState = {
      documentId,
      sessionId: null,
      initialized: false,
      hasMessages: false,
      currentChunk: 0,
      totalChunks: 0,
      lastAccessed: now,
      ...existing,
      ...updates
    }

    this.sessions.set(documentId, sessionState)
    this.saveSessionToStorage(documentId, sessionState)
  }

  /**
   * Mark session as initialized
   */
  markSessionInitialized(
    documentId: string,
    sessionId: string,
    hasMessages: boolean = false,
    currentChunk: number = 0,
    totalChunks: number = 0
  ): void {
    this.updateSessionState(documentId, {
      sessionId,
      initialized: true,
      hasMessages,
      currentChunk,
      totalChunks
    })
  }

  /**
   * Check if session has existing messages (to avoid reprocessing first chunk)
   */
  hasExistingMessages(documentId: string): boolean {
    const session = this.getSessionState(documentId)
    return session?.hasMessages || false
  }

  /**
   * Load session from localStorage
   */
  private loadSessionFromStorage(documentId: string): SessionState | null {
    if (typeof window === 'undefined') return null

    try {
      const sessionData = localStorage.getItem(`${this.STORAGE_KEY_PREFIX}${documentId}`)
      if (sessionData) {
        const session = JSON.parse(sessionData) as SessionState
        console.log(`📱 Loaded session from localStorage for document: ${documentId}`)
        return session
      }
    } catch (error) {
      console.warn(`⚠️ Failed to load session from localStorage:`, error)
    }
    return null
  }

  /**
   * Save session to localStorage
   */
  private saveSessionToStorage(documentId: string, session: SessionState): void {
    if (typeof window === 'undefined') return

    try {
      localStorage.setItem(`${this.STORAGE_KEY_PREFIX}${documentId}`, JSON.stringify(session))
      console.log(`💾 Saved session to localStorage for document: ${documentId}`)
    } catch (error) {
      console.warn(`⚠️ Failed to save session to localStorage:`, error)
    }
  }

  /**
   * Clear session from localStorage
   */
  private clearSessionFromStorage(documentId: string): void {
    if (typeof window === 'undefined') return

    try {
      localStorage.removeItem(`${this.STORAGE_KEY_PREFIX}${documentId}`)
      localStorage.removeItem(`${this.MESSAGES_KEY_PREFIX}${documentId}`)
      this.messageCache.delete(documentId) // Clear cache
      console.log(`🗑️ Cleared session from localStorage for document: ${documentId}`)
    } catch (error) {
      console.warn(`⚠️ Failed to clear session from localStorage:`, error)
    }
  }

  /**
   * Save conversation messages to localStorage (with deduplication)
   */
  saveConversationToStorage(documentId: string, messages: any[]): void {
    if (typeof window === 'undefined') return

    try {
      // Check if messages have actually changed to avoid unnecessary writes
      const cachedMessages = this.messageCache.get(documentId) || []
      if (JSON.stringify(cachedMessages) === JSON.stringify(messages)) {
        return // No changes, skip save
      }

      localStorage.setItem(`${this.MESSAGES_KEY_PREFIX}${documentId}`, JSON.stringify(messages))
      this.messageCache.set(documentId, messages) // Update cache
      console.log(`💾 Saved ${messages.length} messages to localStorage for document: ${documentId}`)
    } catch (error) {
      console.warn(`⚠️ Failed to save messages to localStorage:`, error)
    }
  }

  /**
   * Load conversation messages from localStorage (with caching)
   */
  private messageCache: Map<string, any[]> = new Map()

  loadConversationFromStorage(documentId: string): any[] {
    if (typeof window === 'undefined') return []

    // Check cache first
    if (this.messageCache.has(documentId)) {
      const cached = this.messageCache.get(documentId)!
      console.log(`📱 Loaded ${cached.length} messages from cache for document: ${documentId}`)
      return cached
    }

    try {
      const messagesData = localStorage.getItem(`${this.MESSAGES_KEY_PREFIX}${documentId}`)
      if (messagesData) {
        const messages = JSON.parse(messagesData)
        this.messageCache.set(documentId, messages) // Cache for future use
        console.log(`📱 Loaded ${messages.length} messages from localStorage for document: ${documentId}`)
        return messages
      }
    } catch (error) {
      console.warn(`⚠️ Failed to load messages from localStorage:`, error)
    }
    return []
  }

  /**
   * Clear session state (for logout, document deletion, etc.)
   */
  clearSession(documentId: string): void {
    console.log(`🧹 Clearing session state for document: ${documentId}`)
    this.sessions.delete(documentId)
    this.clearSessionFromStorage(documentId)
  }

  /**
   * Clear all sessions
   */
  clearAllSessions(): void {
    this.sessions.clear()
    this.messageCache.clear()
  }

  /**
   * Get all active sessions (for debugging)
   */
  getActiveSessions(): string[] {
    const now = Date.now()
    const activeSessions: string[] = []

    for (const [documentId, session] of this.sessions.entries()) {
      if (now - session.lastAccessed <= this.SESSION_TIMEOUT) {
        activeSessions.push(documentId)
      } else {
        // Clean up expired sessions
        this.sessions.delete(documentId)
      }
    }

    return activeSessions
  }

  /**
   * Update message count for a session
   */
  updateMessageCount(documentId: string, hasMessages: boolean): void {
    const session = this.getSessionState(documentId)
    if (session) {
      this.updateSessionState(documentId, { hasMessages })
    }
  }

  /**
   * Update current chunk for a session
   */
  updateCurrentChunk(documentId: string, currentChunk: number): void {
    const session = this.getSessionState(documentId)
    if (session) {
      this.updateSessionState(documentId, { currentChunk })
    }
  }
}

// Export singleton instance
export const sessionStateManager = new SessionStateManager()

// Export types for use in components
export type { SessionState }