# Design Document

## Overview

The Zustand State Migration creates a centralized, performant state management system that replaces the current fragmented approach. The design focuses on three main stores: DocumentStore, ChatStore, and SessionStore, each handling specific aspects of the application state with built-in persistence and optimistic updates.

## Architecture

### Core Stores Structure

```
stores/
├── document-store.ts     # Document selection, metadata, progress
├── chat-store.ts         # Messages, AI interactions, streaming
├── session-store.ts      # User sessions, authentication state
└── index.ts             # Store exports and types
```

### State Flow Architecture

```
User Action → Zustand Action → API Call → State Update → UI Re-render
     ↓              ↓              ↓           ↓            ↓
  onClick()    → setLoading()  → fetch()  → setData()  → Component
```

### Persistence Strategy

- **Immediate UI Updates**: Optimistic updates for instant feedback
- **Background Sync**: API calls happen after UI updates
- **Smart Caching**: Intelligent cache invalidation and refresh
- **Offline Support**: Queue actions when offline, sync when online

## Components and Interfaces

### DocumentStore Interface

```typescript
interface DocumentStore {
  // State
  documents: Document[]
  activeDocument: Document | null
  selectedDocument: Document | null
  loading: boolean
  error: string | null
  
  // Actions
  setActiveDocument: (doc: Document) => void
  selectDocument: (doc: Document) => Promise<void>
  loadDocuments: () => Promise<void>
  uploadDocument: (file: File) => Promise<void>
  deleteDocument: (id: string) => Promise<void>
  
  // Computed
  getDocumentById: (id: string) => Document | undefined
  getDocumentProgress: (id: string) => Progress | undefined
}
```

### ChatStore Interface

```typescript
interface ChatStore {
  // State
  messages: Record<string, Message[]>  // documentId -> messages
  currentMessages: Message[]
  streaming: boolean
  loading: boolean
  error: string | null
  
  // Actions
  initializeChat: (documentId: string) => Promise<void>
  sendMessage: (message: string) => Promise<void>
  addMessage: (message: Message) => void
  clearMessages: (documentId?: string) => void
  
  // AI Actions
  triggerAutoResponse: (documentId: string, chunk: string) => Promise<void>
  handleStreamingResponse: (stream: ReadableStream) => Promise<void>
  
  // Computed
  getMessagesForDocument: (documentId: string) => Message[]
  hasConversation: (documentId: string) => boolean
}
```

### SessionStore Interface

```typescript
interface SessionStore {
  // State
  sessions: Record<string, SessionState>
  currentSession: SessionState | null
  initialized: boolean
  
  // Actions
  initializeSession: (documentId: string) => Promise<void>
  updateProgress: (documentId: string, chunk: number) => void
  clearSession: (documentId: string) => void
  
  // Persistence
  saveToStorage: () => void
  loadFromStorage: () => void
  
  // Computed
  getSessionForDocument: (documentId: string) => SessionState | undefined
  isSessionValid: (documentId: string) => boolean
}
```

## Data Models

### Enhanced Message Model

```typescript
interface Message {
  id: string
  role: 'user' | 'assistant' | 'system'
  content: string
  timestamp: string
  documentId: string
  chunkIndex?: number
  streaming?: boolean
  error?: boolean
  metadata?: {
    tokens?: number
    model?: string
    processingTime?: number
  }
}
```

### Document State Model

```typescript
interface DocumentState {
  id: string
  name: string
  status: 'PROCESSING' | 'READY' | 'ERROR'
  totalChunks: number
  currentChunk: number
  progress: number
  lastAccessed: Date
  metadata: {
    fileSize: number
    uploadDate: Date
    processingTime?: number
  }
}
```

### Session State Model

```typescript
interface SessionState {
  documentId: string
  sessionId: string | null
  initialized: boolean
  aiReady: boolean
  currentChunk: number
  totalChunks: number
  hasMessages: boolean
  lastActivity: Date
  metadata: {
    initializationTime?: number
    messageCount: number
    aiModel?: string
  }
}
```

## Error Handling

### Optimistic Updates with Rollback

```typescript
// Example: Send message with optimistic update
const sendMessage = async (content: string) => {
  const tempMessage = createTempMessage(content)
  
  // 1. Immediate UI update
  addMessage(tempMessage)
  
  try {
    // 2. API call
    const response = await api.sendMessage(content)
    
    // 3. Replace temp message with real response
    replaceMessage(tempMessage.id, response)
  } catch (error) {
    // 4. Rollback on error
    removeMessage(tempMessage.id)
    showError(error.message)
  }
}
```

### Automatic Retry Logic

```typescript
const withRetry = async <T>(
  operation: () => Promise<T>,
  maxRetries: number = 3
): Promise<T> => {
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await operation()
    } catch (error) {
      if (i === maxRetries - 1) throw error
      await delay(1000 * Math.pow(2, i)) // Exponential backoff
    }
  }
  throw new Error('Max retries exceeded')
}
```

## Testing Strategy

### Store Testing

```typescript
// Example test structure
describe('ChatStore', () => {
  beforeEach(() => {
    useChatStore.getState().reset()
  })

  it('should add message optimistically', () => {
    const { addMessage, currentMessages } = useChatStore.getState()
    
    addMessage(mockMessage)
    
    expect(currentMessages).toContain(mockMessage)
  })

  it('should handle streaming responses', async () => {
    const { handleStreamingResponse } = useChatStore.getState()
    
    await handleStreamingResponse(mockStream)
    
    expect(useChatStore.getState().streaming).toBe(false)
  })
})
```

### Integration Testing

```typescript
// Test complete user flows
describe('Document Chat Flow', () => {
  it('should handle complete document selection and chat', async () => {
    // 1. Select document
    await useDocumentStore.getState().selectDocument(mockDoc)
    
    // 2. Initialize chat
    await useChatStore.getState().initializeChat(mockDoc.id)
    
    // 3. Send message
    await useChatStore.getState().sendMessage('Hello')
    
    // 4. Verify state
    expect(useChatStore.getState().currentMessages).toHaveLength(2)
  })
})
```

## Performance Optimizations

### Selective Re-rendering

```typescript
// Only re-render when specific state changes
const useDocumentMessages = (documentId: string) => {
  return useChatStore(
    useCallback(
      (state) => state.getMessagesForDocument(documentId),
      [documentId]
    )
  )
}
```

### Computed Values Caching

```typescript
// Cache expensive computations
const getDocumentProgress = useMemo(() => {
  return (documentId: string) => {
    const doc = documents.find(d => d.id === documentId)
    const session = sessions[documentId]
    
    if (!doc || !session) return 0
    
    return Math.round((session.currentChunk / doc.totalChunks) * 100)
  }
}, [documents, sessions])
```

### Background Sync Strategy

```typescript
// Sync state with server in background
const syncWithServer = async () => {
  const { documents, sessions } = getState()
  
  // Batch sync operations
  const syncPromises = documents.map(doc => 
    syncDocumentState(doc.id, sessions[doc.id])
  )
  
  await Promise.allSettled(syncPromises)
}
```

## Migration Strategy

### Phase 1: Core Stores Setup
- Create DocumentStore with basic CRUD operations
- Create ChatStore with message management
- Create SessionStore with persistence

### Phase 2: Dashboard Integration
- Replace useState with Zustand stores in dashboard
- Implement optimistic updates for messages
- Add automatic AI response triggering

### Phase 3: Persistence & Caching
- Implement smart localStorage persistence
- Add background sync capabilities
- Optimize re-rendering with selectors

### Phase 4: Advanced Features
- Add offline support with action queuing
- Implement real-time sync across tabs
- Add comprehensive error boundaries

## Implementation Priorities

1. **Critical Path**: Document selection → Chat initialization → Message sending
2. **User Experience**: Instant UI feedback → Background processing → Error handling
3. **Performance**: Selective re-rendering → Computed caching → Background sync
4. **Reliability**: Optimistic updates → Retry logic → State persistence