"use client"

import { useState, useEffect } from "react"
import { toast } from "sonner"
import { useDocumentStore } from "@/lib/stores/document-store"
import { useChatStore } from "@/lib/stores/chat-store"
import { useSessionStore } from "@/lib/stores/session-store"
import { Button } from "@/components/ui/button"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { MoreHorizontal, Trash2, Edit, RefreshCw } from "lucide-react"
import {
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
  SidebarMenuAction,
} from "@/components/ui/sidebar"
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  <PERSON>ertD<PERSON>og<PERSON>ontent,
  AlertDialogDescription,
  AlertDialog<PERSON>ooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"

interface Document {
  id: string
  filename: string
  status: "PROCESSING" | "READY" | "ERROR"
  errorMessage?: string | null
  totalChunks: number
  createdAt: string
  updatedAt: string
  progress?: {
    currentChunk: number
    completed: boolean
  }
}

interface DocumentListProps {
  refreshTrigger: number
}

export function DocumentList({ refreshTrigger }: DocumentListProps) {
  const [documents, setDocuments] = useState<Document[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState("")
  const [selectedDocumentId, setSelectedDocumentId] = useState<string | null>(null)
  const [isRenaming, setIsRenaming] = useState(false)
  const [newFilename, setNewFilename] = useState("")
  const [documentToRename, setDocumentToRename] = useState<Document | null>(null)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [documentToDelete, setDocumentToDelete] = useState<Document | null>(null)
  const [retryingDocuments, setRetryingDocuments] = useState<Set<string>>(new Set())

  const fetchDocuments = async (retryCount = 0) => {
    const maxRetries = 3
    
    try {
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), 20000) // 20 second timeout
      
      const response = await fetch("/api/documents", {
        signal: controller.signal
      })
      
      clearTimeout(timeoutId)
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ 
          error: "Network error", 
          retryable: true 
        }))
        
        // Handle retryable errors with exponential backoff
        if (errorData.retryable && retryCount < maxRetries) {
          const delay = Math.min(1000 * Math.pow(2, retryCount), 8000)
          console.warn(`Retryable error fetching documents (attempt ${retryCount + 1}/${maxRetries}):`, errorData.details)
          
          setTimeout(() => {
            fetchDocuments(retryCount + 1)
          }, delay)
          return
        }
        
        throw new Error(errorData.details || errorData.error || "Failed to fetch documents")
      }

      const data = await response.json()
      setDocuments(data.documents)
      setError("") // Clear any previous errors
      
    } catch (error: any) {
      console.error("Fetch documents error:", error)
      
      // Handle different types of errors
      if (error.name === 'AbortError') {
        setError("Request timed out. Please check your connection and try again.")
      } else if (error.message.includes('fetch')) {
        setError("Network connection failed. Please check your internet connection.")
      } else {
        setError(error.message || "Failed to load documents")
      }
      
      // Auto-retry for network errors
      if (retryCount < maxRetries && (error.name === 'AbortError' || error.message.includes('fetch'))) {
        const delay = Math.min(2000 * Math.pow(2, retryCount), 10000)
        console.log(`Auto-retrying in ${delay}ms...`)
        
        setTimeout(() => {
          fetchDocuments(retryCount + 1)
        }, delay)
      }
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchDocuments()
  }, [refreshTrigger])

  // Status polling for processing documents
  useEffect(() => {
    const hasProcessingDocuments = documents.some(doc => doc.status === 'PROCESSING')

    if (!hasProcessingDocuments) return

    const pollInterval = setInterval(() => {
      console.log('Polling for document status updates...')
      fetchDocuments()
    }, 3000) // Poll every 3 seconds

    return () => clearInterval(pollInterval)
  }, [documents])

  // Show toast notifications for status changes
  useEffect(() => {
    documents.forEach(doc => {
      if (doc.status === 'READY' && !sessionStorage.getItem(`notified-${doc.id}`)) {
        toast.success(`Document "${doc.filename}" is ready for chat!`)
        sessionStorage.setItem(`notified-${doc.id}`, 'true')
      } else if (doc.status === 'ERROR' && !sessionStorage.getItem(`error-notified-${doc.id}`)) {
        toast.error(`Processing failed for "${doc.filename}": ${doc.errorMessage || 'Unknown error'}`)
        sessionStorage.setItem(`error-notified-${doc.id}`, 'true')
      }
    })
  }, [documents])

  // Listen for document updates from the mock database
  useEffect(() => {
    const handleDocumentsUpdated = () => {
      fetchDocuments()
    }

    window.addEventListener("documentsUpdated", handleDocumentsUpdated)
    return () => {
      window.removeEventListener("documentsUpdated", handleDocumentsUpdated)
    }
  }, [])

  const confirmDelete = (doc: Document) => {
    setDocumentToDelete(doc)
    setIsDeleteDialogOpen(true)
  }

  const handleDelete = async () => {
    if (!documentToDelete) return

    try {
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), 15000) // 15 second timeout for deletion
      
      const response = await fetch(`/api/documents/${documentToDelete.id}`, {
        method: "DELETE",
        signal: controller.signal
      })
      
      clearTimeout(timeoutId)

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: "Delete failed" }))
        throw new Error(errorData.error || "Failed to delete document")
      }

      // Optimistically update UI
      setDocuments((docs) => docs.filter((doc) => doc.id !== documentToDelete.id))

      // Dispatch event to notify dashboard that a document was deleted
      window.dispatchEvent(new CustomEvent("documentDeleted", {
        detail: { documentId: documentToDelete.id }
      }))

      toast.success(`Document "${documentToDelete.filename}" deleted successfully`)
      
    } catch (error: any) {
      console.error("Delete document error:", error)
      
      if (error.name === 'AbortError') {
        setError("Delete operation timed out. Please try again.")
        toast.error("Delete operation timed out")
      } else if (error.message.includes('fetch')) {
        setError("Network error during deletion. Please check your connection.")
        toast.error("Network error - please try again")
      } else {
        setError(error.message || "Failed to delete document")
        toast.error(error.message || "Failed to delete document")
      }
    } finally {
      setIsDeleteDialogOpen(false)
      setDocumentToDelete(null)
    }
  }

  const startRename = (doc: Document) => {
    setDocumentToRename(doc)
    setNewFilename(doc.filename)
    setIsRenaming(true)
  }

  const handleRename = async () => {
    if (!documentToRename || !newFilename.trim()) return

    try {
      const response = await fetch(`/api/documents/${documentToRename.id}/rename`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ newFilename: newFilename.trim() }),
      })

      if (!response.ok) throw new Error("Failed to rename document")

      const updatedDoc = await response.json()
      setDocuments((docs) =>
        docs.map((doc) => (doc.id === updatedDoc.id ? { ...doc, filename: updatedDoc.filename } : doc)),
      )
      setIsRenaming(false)
      setDocumentToRename(null)
      setNewFilename("")
    } catch (error: any) {
      setError(error.message)
    }
  }

  const handleSelectDocument = async (doc: Document) => {
    setSelectedDocumentId(doc.id)

    try {
      // Use Zustand stores directly instead of events
      const { selectDocument } = useDocumentStore.getState()
      const { initializeChat, triggerAutoResponse } = useChatStore.getState()
      const { initializeSession } = useSessionStore.getState()

      // Select the document
      await selectDocument({
        id: doc.id,
        name: doc.filename,
        fileName: doc.filename,
        status: doc.status,
        totalChunks: doc.totalChunks || 0,
        createdAt: doc.createdAt,
        updatedAt: doc.updatedAt
      })

      // Initialize session and chat
      await initializeSession(doc.id)
      await initializeChat(doc.id)
      await triggerAutoResponse(doc.id, "Let's start learning about this document.")

      console.log('✅ Document selection and initialization complete')
    } catch (error) {
      console.error('❌ Failed to select document:', error)
      toast.error('Failed to select document')
    }
  }

  const handleRetry = async (doc: Document) => {
    if (retryingDocuments.has(doc.id)) return // Prevent multiple retries

    setRetryingDocuments(prev => new Set(prev).add(doc.id))

    try {
      const response = await fetch(`/api/documents/${doc.id}/retry`, {
        method: "POST",
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Failed to retry processing")
      }

      // Update document status optimistically
      setDocuments(docs =>
        docs.map(d =>
          d.id === doc.id
            ? { ...d, status: 'PROCESSING' as const, errorMessage: null }
            : d
        )
      )

      console.log(`Retry initiated for document ${doc.id}`)
      toast.success("Document processing retry initiated")
    } catch (error: any) {
      console.error("Retry failed:", error)
      toast.error(`Failed to retry processing: ${error.message}`)
      setError(`Failed to retry processing: ${error.message}`)
    } finally {
      setRetryingDocuments(prev => {
        const newSet = new Set(prev)
        newSet.delete(doc.id)
        return newSet
      })
    }
  }


  const getStatusText = (status: Document["status"]) => {
    switch (status) {
      case "PROCESSING":
        return "Processing..."
      case "READY":
        return "Ready"
      case "ERROR":
        return "Error"
      default:
        return ""
    }
  }


  if (loading) {
    return (
      <SidebarMenu>
        {Array.from({ length: 3 }).map((_, i) => (
          <SidebarMenuItem key={i}>
            <SidebarMenuButton>
              <div className="h-4 bg-muted rounded w-3/4 animate-pulse" />
            </SidebarMenuButton>
          </SidebarMenuItem>
        ))}
      </SidebarMenu>
    )
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertDescription className="font-body">{error}</AlertDescription>
      </Alert>
    )
  }

  if (documents.length === 0) {
    return null
  }

  return (
    <>
      <SidebarMenu>
        {documents.map((doc) => (
          <SidebarMenuItem key={doc.id}>
            <SidebarMenuButton
              isActive={selectedDocumentId === doc.id}
              onClick={() => doc.status === "READY" ? handleSelectDocument(doc) : undefined}
              disabled={doc.status !== "READY"}
              className={doc.status !== "READY" ? "opacity-60 cursor-not-allowed" : ""}
            >
              <div className="flex flex-col flex-1 min-w-0">
                <span className="truncate-filename font-body text-sm" title={doc.filename}>
                  {doc.filename}
                </span>
                {doc.status === "READY" && doc.progress && doc.totalChunks > 0 && (
                  <span className="text-xs text-muted-foreground">
                    Chunk {doc.progress.currentChunk + 1}/{doc.totalChunks}
                  </span>
                )}
                {doc.status !== "READY" && (
                  <span className="text-xs text-muted-foreground">
                    {getStatusText(doc.status)}
                  </span>
                )}
              </div>

            </SidebarMenuButton>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <SidebarMenuAction showOnHover>
                  <MoreHorizontal />
                </SidebarMenuAction>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                side="right"
                align="start"
                className="bg-white text-card-foreground border border-border shadow-sm"
              >
                {doc.status === "ERROR" && (
                  <DropdownMenuItem
                    onClick={() => handleRetry(doc)}
                    disabled={retryingDocuments.has(doc.id)}
                    className="hover:bg-accent hover:text-accent-foreground font-body"
                  >
                    <RefreshCw className={`mr-2 h-4 w-4 ${retryingDocuments.has(doc.id) ? 'animate-spin' : ''}`} />
                    {retryingDocuments.has(doc.id) ? 'Retrying...' : 'Retry Processing'}
                  </DropdownMenuItem>
                )}
                <DropdownMenuItem
                  onClick={() => startRename(doc)}
                  className="hover:bg-accent hover:text-accent-foreground font-body"
                >
                  <Edit className="mr-2 h-4 w-4" />
                  Rename
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => confirmDelete(doc)}
                  className="text-destructive hover:bg-destructive/10 hover:text-destructive font-body"
                >
                  <Trash2 className="mr-2 h-4 w-4" />
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </SidebarMenuItem>
        ))}
      </SidebarMenu>

      {/* Rename Dialog */}
      <Dialog open={isRenaming} onOpenChange={setIsRenaming}>
        <DialogContent className="sm:max-w-[425px] bg-white text-card-foreground border border-border shadow-sm">
          <DialogHeader>
            <DialogTitle className="font-heading">Rename Document</DialogTitle>
            <DialogDescription className="text-muted-foreground font-body">
              Enter a new name for &quot;{documentToRename?.filename}&quot;.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="newFilename" className="text-foreground text-right font-body">
                New Name
              </Label>
              <Input
                id="newFilename"
                value={newFilename}
                onChange={(e) => setNewFilename(e.target.value)}
                className="col-span-3 bg-input text-foreground border-border focus:ring-primary font-body"
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsRenaming(false)}
              className="bg-transparent border-border text-foreground hover:bg-accent hover:text-accent-foreground font-body"
            >
              Cancel
            </Button>
            <Button onClick={handleRename} className="bg-primary hover:bg-primary/90 text-primary-foreground font-body">
              Rename
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent className="bg-white text-card-foreground border border-border shadow-sm">
          <AlertDialogHeader>
            <AlertDialogTitle className="font-heading">Are you absolutely sure?</AlertDialogTitle>
            <AlertDialogDescription className="font-body">
              This action cannot be undone. This will permanently delete the document &quot;
              <span className="font-semibold">{documentToDelete?.filename}</span>&quot; and all associated chat history and
              progress.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel className="bg-transparent border-border text-foreground hover:bg-accent hover:text-accent-foreground font-body">
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              className="bg-destructive hover:bg-destructive/90 text-destructive-foreground font-body"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}
