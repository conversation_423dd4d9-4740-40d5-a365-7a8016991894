import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { dbService } from '@/lib/db-service'
import { AIProviderFactory } from '@/lib/ai'
import { EducationalPromptBuilder } from '@/lib/ai/educational-prompts'
import { memoryService, BloomLevel } from '@/lib/ai/memory-service'
import { buildEducationalContext } from '@/lib/ai/memory-integration'

/**
 * Chunk Navigation API Endpoint
 * Handles moving to next/previous chunks with AI context transition
 */

interface NextChunkRequest {
  documentId: string
  currentChunk: number
  direction?: 'next' | 'previous'
}

/**
 * POST /api/chat/next-chunk
 * Navigate to next chunk and generate AI transition response
 */
export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const supabase = await createClient()
    const {
      data: { session },
    } = await supabase.auth.getSession()

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Parse request body
    const {
      documentId,
      currentChunk,
      direction = 'next'
    }: NextChunkRequest = await request.json()

    // Validate required fields
    if (!documentId || currentChunk === undefined) {
      return NextResponse.json(
        { error: 'Missing required fields: documentId, currentChunk' },
        { status: 400 }
      )
    }

    // Verify document exists and belongs to user
    const document = await dbService.document.findFirst({
      where: {
        id: documentId,
        userId: session.user.id
      }
    })

    if (!document) {
      return NextResponse.json(
        { error: 'Document not found or access denied' },
        { status: 404 }
      )
    }

    // Validate current chunk matches user's actual progress
    const userProgress = await dbService.progress.findUnique({
      where: {
        userId_documentId: {
          userId: session.user.id,
          documentId
        }
      }
    })

    if (!userProgress) {
      return NextResponse.json(
        { error: 'User progress not found' },
        { status: 404 }
      )
    }

    // Ensure sequential progression - user can only move from their current chunk
    if (currentChunk !== userProgress.currentChunk) {
      return NextResponse.json(
        {
          error: `Invalid chunk progression. You are currently on chunk ${userProgress.currentChunk + 1}, cannot navigate from chunk ${currentChunk + 1}`,
          currentChunk: userProgress.currentChunk
        },
        { status: 400 }
      )
    }

    // Calculate target chunk index (only allow +1 or -1 movement)
    const targetChunkIndex = direction === 'next'
      ? currentChunk + 1
      : currentChunk - 1

    // Validate chunk bounds
    if (targetChunkIndex < 0 || targetChunkIndex >= document.totalChunks) {
      return NextResponse.json(
        {
          error: `Cannot navigate ${direction}. ${targetChunkIndex < 0 ? 'Already at first chunk' : 'Already at last chunk'
            }`,
          currentChunk: currentChunk
        },
        { status: 400 }
      )
    }

    // Additional validation: prevent skipping chunks (only allow sequential movement)
    if (direction === 'next' && targetChunkIndex > currentChunk + 1) {
      return NextResponse.json(
        {
          error: 'Cannot skip chunks. You must progress sequentially.',
          currentChunk: currentChunk
        },
        { status: 400 }
      )
    }

    if (direction === 'previous' && targetChunkIndex < currentChunk - 1) {
      return NextResponse.json(
        {
          error: 'Cannot skip chunks backwards. You must move sequentially.',
          currentChunk: currentChunk
        },
        { status: 400 }
      )
    }

    // Get the target chunk
    const targetChunk = await dbService.chunk.findUnique({
      where: {
        documentId_chunkIndex: {
          documentId,
          chunkIndex: targetChunkIndex
        }
      }
    })

    if (!targetChunk) {
      return NextResponse.json(
        { error: 'Target chunk not found' },
        { status: 404 }
      )
    }

    // Get user's progress to find session ID
    const progress = await dbService.progress.findUnique({
      where: {
        userId_documentId: {
          userId: session.user.id,
          documentId
        }
      }
    })

    if (!progress?.sessionId) {
      return NextResponse.json(
        { error: 'AI session not initialized' },
        { status: 400 }
      )
    }

    // Ensure sessionId is not null
    const sessionId = progress.sessionId
    if (!sessionId) {
      return NextResponse.json(
        { error: 'Invalid session ID' },
        { status: 400 }
      )
    }

    // Update progress to new chunk
    await dbService.progress.update({
      where: {
        userId_documentId: {
          userId: session.user.id,
          documentId
        }
      },
      data: {
        currentChunk: targetChunkIndex
      }
    })

    // Generate AI transition response with memory context
    const aiProvider = AIProviderFactory.createProvider()
    await aiProvider.initialize()

    // Get previous chunk summary from memory
    const previousChunkMemories = await memoryService.getContextualMemory(
      documentId,
      session.user.id,
      `chunk ${currentChunk} summary understanding`
    )

    const previousSummary = previousChunkMemories.length > 0
      ? previousChunkMemories[0].memory
      : `Completed chunk ${currentChunk}`

    // Build chunk transition prompt with memory context
    const transitionPrompt = EducationalPromptBuilder.buildChunkTransitionPrompt(
      targetChunkIndex,
      document.totalChunks,
      previousSummary,
      targetChunk.content
    )

    // Build conversation context for AI with memory integration
    const context = {
      userId: session.user.id,
      documentId,
      chunkIndex: targetChunkIndex,
      chunkContent: targetChunk.content,
      sessionId,
      currentQuery: `Navigate to chunk ${targetChunkIndex + 1} - introduce new content and maintain learning continuity`
    }

    // Build educational context using memory service
    const educationalContext = await buildEducationalContext(context)

    // Generate AI transition response with educational context
    let aiResponse = ''
    const enhancedContext = {
      ...context,
      educationalContext,
      previousSummary
    }
    const responseGenerator = aiProvider.generateResponse(transitionPrompt, enhancedContext)

    for await (const token of responseGenerator) {
      aiResponse += token
    }

    // Store chunk transition in memory for future context
    await memoryService.addLearningMemory(
      documentId,
      session.user.id,
      `Transitioned to chunk ${targetChunkIndex + 1}: ${aiResponse.substring(0, 200)}...`,
      'chunk_transition',
      BloomLevel.UNDERSTAND
    )

    // Store the new chunk introduction in conversation memory
    await memoryService.addConversationMessage(
      documentId,
      session.user.id,
      aiResponse,
      'assistant'
    )

    // Check if this is the final chunk
    const isCompleted = targetChunkIndex >= document.totalChunks - 1

    // Return navigation result with enhanced context
    return NextResponse.json({
      success: true,
      currentChunk: targetChunkIndex,
      chunkContent: targetChunk.content,
      aiResponse: aiResponse, // Keep consistent naming
      totalChunks: document.totalChunks,
      completed: isCompleted,
      progress: {
        current: targetChunkIndex + 1,
        total: document.totalChunks,
        percentage: Math.round(((targetChunkIndex + 1) / document.totalChunks) * 100)
      },
      navigation: {
        canGoNext: targetChunkIndex < document.totalChunks - 1,
        canGoPrevious: targetChunkIndex > 0,
        direction: direction,
        transitionType: direction === 'next' ? 'forward_progression' : 'review_previous'
      },
      memoryContext: {
        sessionId,
        hasMemoryContext: previousChunkMemories.length > 0,
        contextSummary: previousSummary
      }
    })

  } catch (error) {
    console.error('Chunk navigation API error:', error)

    return NextResponse.json(
      {
        error: 'Failed to navigate to chunk',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
