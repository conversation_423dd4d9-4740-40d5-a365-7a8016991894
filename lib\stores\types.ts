/**
 * Shared types for Zustand stores
 * Keep this file under 100 lines - split if needed
 */

// Core domain types
export interface Document {
  id: string
  name: string
  fileName: string
  status: 'PROCESSING' | 'READY' | 'ERROR'
  totalChunks: number
  createdAt: string
  updatedAt: string
  errorMessage?: string
}

export interface Message {
  id: string
  role: 'user' | 'assistant' | 'system'
  content: string
  timestamp: string
  documentId: string
  chunkIndex?: number
  streaming?: boolean
  isThinking?: boolean
  error?: boolean
  metadata?: {
    tokens?: number
    model?: string
    processingTime?: number
  }
}

export interface SessionState {
  documentId: string
  sessionId: string | null
  initialized: boolean
  aiReady: boolean
  currentChunk: number
  totalChunks: number
  hasMessages: boolean
  lastActivity: Date
}

export interface Progress {
  documentId: string
  currentChunk: number
  totalChunks: number
  percentage: number
  lastUpdated: Date
}

// Store state interfaces
export interface DocumentStoreState {
  documents: Document[]
  activeDocument: Document | null
  selectedDocument: Document | null
  loading: boolean
  error: string | null
}

export interface ChatStoreState {
  messages: Record<string, Message[]>
  currentDocumentId: string | null
  streaming: boolean
  loading: boolean
  initializing: boolean
  error: string | null
}

export interface SessionStoreState {
  sessions: Record<string, SessionState>
  currentSession: SessionState | null
  initialized: boolean
}

// Action types for better type safety
export type DocumentActions = {
  setActiveDocument: (doc: Document | null) => void
  selectDocument: (doc: Document | null) => Promise<void>
  loadDocuments: () => Promise<void>
  uploadDocument: (file: File) => Promise<void>
  deleteDocument: (id: string) => Promise<void>
  updateDocumentStatus: (id: string, status: Document['status']) => void
  clearError: () => void
}

export type ChatActions = {
  initializeChat: (documentId: string) => Promise<void>
  sendMessage: (message: string) => Promise<void>
  addMessage: (message: Message) => void
  updateMessage: (id: string, updates: Partial<Message>) => void
  removeMessage: (id: string) => void
  clearMessages: (documentId?: string) => void
  setStreaming: (streaming: boolean) => void
  triggerAutoResponse: (documentId: string, chunk: string) => Promise<void>
}

export type SessionActions = {
  initializeSession: (documentId: string) => Promise<void>
  updateProgress: (documentId: string, chunk: number) => void
  clearSession: (documentId: string) => void
  markAIReady: (documentId: string) => void
  updateLastActivity: (documentId: string) => void
}