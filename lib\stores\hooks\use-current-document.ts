/**
 * Utility hook for current document state
 * Combines document and session state for convenience
 */

import { useDocumentStore } from '../document-store'
import { useSessionStore } from '../session-store'
import { useMemo } from 'react'


export function useCurrentDocument() {
  const selectedDocument = useDocumentStore(state => state.selectedDocument)
  const activeDocument = useDocumentStore(state => state.activeDocument)
  const currentSession = useSessionStore(state => state.currentSession)
  
  const currentDocument = selectedDocument || activeDocument
  
  const progress = useMemo(() => {
    if (!currentDocument || !currentSession) return null
    
    return {
      current: currentSession.currentChunk + 1,
      total: currentSession.totalChunks,
      percentage: Math.round(((currentSession.currentChunk + 1) / currentSession.totalChunks) * 100)
    }
  }, [currentDocument, currentSession])
  
  return {
    document: currentDocument,
    session: currentSession,
    progress,
    isReady: currentDocument?.status === 'READY',
    hasDocument: !!currentDocument
  }
}