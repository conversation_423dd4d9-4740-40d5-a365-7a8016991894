/**
 * Utility hook for document session management
 * Combines session operations with document context
 */

import { useSessionStore } from '../session-store'
import { useDocumentStore } from '../document-store'
import { useCallback, useMemo } from 'react'


export function useDocumentSession(documentId?: string) {
  const sessions = useSessionStore(state => state.sessions)
  const selectedDocument = useDocumentStore(state => state.selectedDocument)
  const initializeSession = useSessionStore(state => state.initializeSession)
  const updateProgress = useSessionStore(state => state.updateProgress)
  const clearSession = useSessionStore(state => state.clearSession)
  
  const targetDocumentId = documentId || selectedDocument?.id
  
  const session = useMemo(() => {
    if (!targetDocumentId) return null
    return sessions[targetDocumentId] || null
  }, [sessions, targetDocumentId])
  
  const initialize = useCallback(async () => {
    if (!targetDocumentId) return
    await initializeSession(targetDocumentId)
  }, [initializeSession, targetDocumentId])
  
  const updateChunk = useCallback((chunk: number) => {
    if (!targetDocumentId) return
    updateProgress(targetDocumentId, chunk)
  }, [updateProgress, targetDocumentId])
  
  const clear = useCallback(() => {
    if (!targetDocumentId) return
    clearSession(targetDocumentId)
  }, [clearSession, targetDocumentId])
  
  return {
    session,
    initialize,
    updateChunk,
    clear,
    isInitialized: session?.initialized || false,
    isAIReady: session?.aiReady || false,
    currentChunk: session?.currentChunk || 0,
    hasSession: !!session
  }
}