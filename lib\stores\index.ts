/**
 * Zustand stores entry point
 * Exports all stores and types
 */

// Store exports
export { useDocumentStore } from './document-store'
export { useChatStore } from './chat-store'
export { useSessionStore } from './session-store'

// Type exports
export type {
  Document,
  Message,
  SessionState,
  Progress,
  DocumentStoreState,
  ChatStoreState,
  SessionStoreState,
  DocumentActions,
  ChatActions,
  SessionActions
} from './types'

// Utility hooks for common patterns
export { useCurrentDocument } from './hooks/use-current-document'
export { useDocumentMessages } from './hooks/use-document-messages'
export { useDocumentSession } from './hooks/use-document-session'