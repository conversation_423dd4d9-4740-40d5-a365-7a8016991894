import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { fallbackManager } from '@/lib/ai/fallback-manager'
import { memoryService } from '@/lib/ai/memory-service'

/**
 * System Status API Endpoint
 * Provides comprehensive status information about AI services
 */

interface SystemStatus {
  timestamp: string
  services: {
    ai: {
      status: 'healthy' | 'degraded' | 'unavailable'
      currentProvider: string
      availableProviders: string[]
      failedProviders: string[]
      hasFallback: boolean
    }
    memory: {
      status: 'healthy' | 'degraded' | 'unavailable'
      available: boolean
      message: string
    }
    database: {
      status: 'healthy' | 'unavailable'
      message: string
    }
  }
  overall: 'healthy' | 'degraded' | 'unavailable'
}

/**
 * GET /api/system/status
 * Get comprehensive system status
 */
export async function GET(_request: NextRequest) {
  try {
    const status: SystemStatus = {
      timestamp: new Date().toISOString(),
      services: {
        ai: {
          status: 'healthy',
          currentProvider: '',
          availableProviders: [],
          failedProviders: [],
          hasFallback: false
        },
        memory: {
          status: 'healthy',
          available: false,
          message: ''
        },
        database: {
          status: 'healthy',
          message: 'Connected'
        }
      },
      overall: 'healthy'
    }

    // Check AI service status
    try {
      const aiStatus = fallbackManager.getStatus()
      status.services.ai = {
        status: aiStatus.availableProviders.length > 0 ? 'healthy' : 'unavailable',
        currentProvider: aiStatus.currentProvider,
        availableProviders: aiStatus.availableProviders,
        failedProviders: aiStatus.failedProviders,
        hasFallback: aiStatus.hasFallback
      }

      if (aiStatus.failedProviders.length > 0) {
        status.services.ai.status = 'degraded'
      }
    } catch {
      status.services.ai.status = 'unavailable'
    }

    // Check memory service status
    try {
      const memoryAvailable = memoryService.isAvailable()
      const connectionTest = await memoryService.testConnection()
      
      status.services.memory = {
        status: memoryAvailable && connectionTest ? 'healthy' : 'degraded',
        available: memoryAvailable,
        message: memoryAvailable 
          ? (connectionTest ? 'Connected and operational' : 'Connected but degraded')
          : 'Running in degraded mode - memory features disabled'
      }
    } catch {
      status.services.memory = {
        status: 'unavailable',
        available: false,
        message: 'Memory service unavailable'
      }
    }

    // Check database status
    try {
      const supabase = await createClient()
      const { error } = await supabase.from('documents').select('id').limit(1)
      
      if (error) {
        status.services.database = {
          status: 'unavailable',
          message: `Database error: ${error.message}`
        }
      }
    } catch {
      status.services.database = {
        status: 'unavailable',
        message: 'Database connection failed'
      }
    }

    // Determine overall status
    const serviceStatuses = Object.values(status.services).map(s => s.status)
    
    if (serviceStatuses.includes('unavailable')) {
      status.overall = 'unavailable'
    } else if (serviceStatuses.includes('degraded')) {
      status.overall = 'degraded'
    } else {
      status.overall = 'healthy'
    }

    return NextResponse.json(status)

  } catch (error) {
    console.error('System status check error:', error)
    
    return NextResponse.json(
      {
        timestamp: new Date().toISOString(),
        error: 'Failed to check system status',
        details: error instanceof Error ? error.message : 'Unknown error',
        overall: 'unavailable'
      },
      { status: 500 }
    )
  }
}

/**
 * POST /api/system/status/reset
 * Reset failed providers and attempt recovery
 */
export async function POST(_request: NextRequest) {
  try {
    // Authenticate user (optional - could be admin only)
    const supabase = await createClient()
    const {
      data: { session },
    } = await supabase.auth.getSession()

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Reset failed providers
    fallbackManager.resetFailedProviders()

    // Try to reinitialize memory service
    try {
      await memoryService.initialize()
    } catch (error) {
      console.log('Memory service reset failed:', error)
    }

    return NextResponse.json({
      message: 'System recovery initiated',
      timestamp: new Date().toISOString(),
      actions: [
        'Reset failed AI providers',
        'Attempted memory service reinitialization'
      ]
    })

  } catch (error) {
    console.error('System reset error:', error)
    
    return NextResponse.json(
      {
        error: 'Failed to reset system',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
