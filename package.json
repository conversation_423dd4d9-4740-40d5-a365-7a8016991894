{"name": "my-v0-project", "version": "0.1.0", "private": true, "type": "module", "scripts": {"build": "npx prisma generate && npm run type-check && npm run lint && next build", "dev": "npm run type-check && next dev", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit --skipL<PERSON><PERSON><PERSON><PERSON>", "pre-commit": "npm run type-check && npm run lint", "start": "next start", "prepare": "husky", "postinstall": "npx prisma generate"}, "dependencies": {"@anthropic-ai/sdk": "^0.57.0", "@hookform/resolvers": "^3.9.1", "@prisma/client": "^6.12.0", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "1.1.2", "@radix-ui/react-checkbox": "1.1.3", "@radix-ui/react-collapsible": "1.1.2", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "latest", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "latest", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slider": "1.2.2", "@radix-ui/react-slot": "latest", "@radix-ui/react-switch": "1.1.2", "@radix-ui/react-tabs": "1.1.2", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "1.1.1", "@radix-ui/react-tooltip": "1.1.6", "@supabase/auth-helpers-nextjs": "latest", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "latest", "@upstash/redis": "^1.35.1", "autoprefixer": "^10.4.20", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "date-fns": "4.1.0", "dotenv": "^17.2.0", "embla-carousel-react": "8.5.1", "input-otp": "1.4.1", "llamaindex": "^0.11.19", "lru-cache": "^11.1.0", "lucide-react": "^0.454.0", "mem0ai": "^2.1.36", "next": "15.2.4", "next-themes": "^0.4.4", "openai": "^5.10.2", "prisma": "^6.12.0", "react": "^19", "react-day-picker": "8.10.1", "react-dom": "^19", "react-hook-form": "^7.54.1", "react-resizable-panels": "^2.1.7", "recharts": "2.15.0", "redis": "^5.6.1", "sonner": "^1.7.1", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.6", "zod": "^3.24.1", "zustand": "^5.0.7"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@types/jest": "^30.0.0", "@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9.32.0", "eslint-config-next": "15.4.4", "husky": "^9.1.7", "jest": "^30.0.5", "lint-staged": "^16.1.2", "postcss": "^8.5", "tailwindcss": "^3.4.17", "ts-jest": "^29.4.0", "typescript": "^5"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix"]}}