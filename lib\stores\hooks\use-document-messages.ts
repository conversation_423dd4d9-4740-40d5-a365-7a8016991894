/**
 * Utility hook for document-specific messages
 * Provides messages for a specific document with memoization
 */

import { useChatStore } from '../chat-store'
import { useCallback, useMemo } from 'react'
import type { Message } from '../types'

export function useDocumentMessages(documentId?: string) {
  const messages = useChatStore(state => state.messages)
  const currentDocumentId = useChatStore(state => state.currentDocumentId)
  const addMessage = useChatStore(state => state.addMessage)
  const updateMessage = useChatStore(state => state.updateMessage)
  const removeMessage = useChatStore(state => state.removeMessage)
  
  const targetDocumentId = documentId || currentDocumentId
  
  const documentMessages = useMemo(() => {
    if (!targetDocumentId) return []
    return messages[targetDocumentId] || []
  }, [messages, targetDocumentId])
  
  const addDocumentMessage = useCallback((message: Omit<Message, 'documentId'>) => {
    if (!targetDocumentId) return
    
    addMessage({
      ...message,
      documentId: targetDocumentId
    })
  }, [addMessage, targetDocumentId])
  
  const hasMessages = documentMessages.length > 0
  const lastMessage = documentMessages[documentMessages.length - 1]
  const isStreaming = documentMessages.some(msg => msg.streaming)
  
  return {
    messages: documentMessages,
    addMessage: addDocumentMessage,
    updateMessage,
    removeMessage,
    hasMessages,
    lastMessage,
    isStreaming,
    count: documentMessages.length
  }
}