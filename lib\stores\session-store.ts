/**
 * Session Store - Manages user sessions and AI state
 * Handles session initialization, progress tracking, and persistence
 * Max 150 lines - split if needed
 */

import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import type { SessionState, SessionStoreState, SessionActions } from './types'

type SessionStore = SessionStoreState & SessionActions

const SESSION_TIMEOUT = 30 * 60 * 1000 // 30 minutes

export const useSessionStore = create<SessionStore>()(
  persist(
    (set, get) => ({
      // Initial state
      sessions: {},
      currentSession: null,
      initialized: false,

      // Actions
      initializeSession: async (documentId) => {
        // Check if session already exists and is valid
        const existingSession = get().sessions[documentId]
        if (existingSession && isSessionValid(existingSession)) {
          set({ currentSession: existingSession })
          return
        }

        try {
          // Create new session
          const newSession: SessionState = {
            documentId,
            sessionId: null,
            initialized: false,
            aiReady: false,
            currentChunk: 0,
            totalChunks: 0,
            hasMessages: false,
            lastActivity: new Date()
          }

          // Update state immediately
          set(state => ({
            sessions: {
              ...state.sessions,
              [documentId]: newSession
            },
            currentSession: newSession
          }))

          // Initialize with server
          const response = await fetch('/api/chat/initialize', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ documentId })
          })

          if (response.ok) {
            const data = await response.json()
            
            // Update session with server data
            const updatedSession: SessionState = {
              ...newSession,
              sessionId: data.sessionId,
              initialized: true,
              totalChunks: data.totalChunks || 0,
              hasMessages: data.messages?.length > 0 || false
            }

            set(state => ({
              sessions: {
                ...state.sessions,
                [documentId]: updatedSession
              },
              currentSession: updatedSession,
              initialized: true
            }))
          }

        } catch (error) {
          console.error('Failed to initialize session:', error)
          
          // Keep the basic session even if server init fails
          set(state => ({
            sessions: {
              ...state.sessions,
              [documentId]: {
                ...state.sessions[documentId],
                initialized: false
              }
            }
          }))
        }
      },

      updateProgress: (documentId, chunk) => {
        set(state => {
          const session = state.sessions[documentId]
          if (!session) return state

          const updatedSession = {
            ...session,
            currentChunk: chunk,
            lastActivity: new Date()
          }

          return {
            sessions: {
              ...state.sessions,
              [documentId]: updatedSession
            },
            currentSession: state.currentSession?.documentId === documentId 
              ? updatedSession 
              : state.currentSession
          }
        })

        // Sync with server in background
        syncProgressWithServer(documentId, chunk).catch(console.warn)
      },

      clearSession: (documentId) => {
        set(state => {
          const newSessions = { ...state.sessions }
          delete newSessions[documentId]

          return {
            sessions: newSessions,
            currentSession: state.currentSession?.documentId === documentId 
              ? null 
              : state.currentSession
          }
        })

        // Clear from localStorage
        clearSessionFromStorage(documentId)
      },

      markAIReady: (documentId) => {
        set(state => {
          const session = state.sessions[documentId]
          if (!session) return state

          const updatedSession = {
            ...session,
            aiReady: true,
            lastActivity: new Date()
          }

          return {
            sessions: {
              ...state.sessions,
              [documentId]: updatedSession
            },
            currentSession: state.currentSession?.documentId === documentId 
              ? updatedSession 
              : state.currentSession
          }
        })
      },

      updateLastActivity: (documentId) => {
        set(state => {
          const session = state.sessions[documentId]
          if (!session) return state

          const updatedSession = {
            ...session,
            lastActivity: new Date()
          }

          return {
            sessions: {
              ...state.sessions,
              [documentId]: updatedSession
            },
            currentSession: state.currentSession?.documentId === documentId 
              ? updatedSession 
              : state.currentSession
          }
        })
      }
    }),
    {
      name: 'session-store',
      partialize: (state) => ({
        sessions: state.sessions
      })
    }
  )
)

// Helper functions
function isSessionValid(session: SessionState): boolean {
  const now = new Date().getTime()
  const lastActivity = new Date(session.lastActivity).getTime()
  return (now - lastActivity) < SESSION_TIMEOUT
}

async function syncProgressWithServer(documentId: string, chunk: number) {
  try {
    await fetch(`/api/documents/${documentId}/progress`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ currentChunk: chunk })
    })
  } catch (error) {
    console.warn('Failed to sync progress with server:', error)
  }
}

function clearSessionFromStorage(documentId: string) {
  try {
    localStorage.removeItem(`guided-tutor-session-${documentId}`)
    localStorage.removeItem(`guided-tutor-messages-${documentId}`)
  } catch (error) {
    console.warn('Failed to clear session from storage:', error)
  }
}