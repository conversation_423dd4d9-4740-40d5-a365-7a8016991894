import { type NextRequest, NextResponse } from "next/server"
import { createClient } from "@/lib/supabase/server"
import { dbService } from "@/lib/db-service"
import { jobQueue } from "@/lib/job-queue"
import { cacheService } from "@/lib/cache-service"
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/lib/error-handler"
import { TempFileService } from "@/lib/temp-file-service"

/**
 * Optimized document processing API endpoint
 * Uses job queue and caching for better performance
 */
export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient()
    const {
      data: { session },
    } = await supabase.auth.getSession()

    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    let body;
    try {
      body = await request.json()
    } catch {
      return NextResponse.json({ error: "Invalid JSON in request body" }, { status: 400 })
    }

    const { documentId } = body

    if (!documentId || typeof documentId !== 'string') {
      return NextResponse.json({ error: "Valid document ID is required" }, { status: 400 })
    }

    // Additional UUID format validation (optional but recommended)
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
    if (!uuidRegex.test(documentId)) {
      return NextResponse.json({ error: "Document ID must be a valid UUID" }, { status: 400 })
    }

    // Check if document is already being processed
    if (cacheService.isProcessing(documentId)) {
      const existingJobId = cacheService.getProcessingJobId(documentId)
      return NextResponse.json({
        success: true,
        message: "Document is already being processed",
        status: 'PROCESSING',
        ...(existingJobId && { jobId: existingJobId })
      })
    }

    // Check if document is already in cache
    const cachedDocument = cacheService.getDocument(documentId)
    if (cachedDocument && cachedDocument.status === 'READY') {
      return NextResponse.json({
        success: true,
        message: "Document already processed (from cache)",
        status: 'READY',
        totalChunks: cachedDocument.totalChunks,
        fromCache: true
      })
    }

    // Fetch document from database
    const document = await dbService.document.findFirst({
      where: {
        id: documentId,
        userId: session.user.id // Ensure user owns the document
      }
    })

    if (!document) {
      return NextResponse.json({ error: "Document not found" }, { status: 404 })
    }

    // Check if document is already processed in database
    if (document.status === 'READY') {
      // Cache the document chunks for faster access
      const chunks = await dbService.chunk.findMany({
        where: { documentId },
        orderBy: { chunkIndex: 'asc' }
      })

      // Store in cache
      cacheService.setDocument(documentId, {
        chunks: chunks.map((chunk: any) => ({
          chunkIndex: chunk.chunkIndex,
          content: chunk.content,
          pageNumber: chunk.pageNumber
        })),
        totalChunks: document.totalChunks,
        status: 'READY'
      })

      return NextResponse.json({
        success: true,
        message: "Document already processed",
        status: 'READY',
        totalChunks: document.totalChunks
      })
    }

    // Update document status to PROCESSING
    await dbService.document.update({
      where: { id: documentId },
      data: {
        status: 'PROCESSING',
        errorMessage: null
      }
    })

    // Retrieve file from Supabase Storage
    const { data: fileData, error: downloadError } = await supabase.storage
      .from('documents')
      .download(`${session.user.id}/${document.filePath}`)

    if (downloadError || !fileData) {
      cacheService.clearProcessing(documentId)

      await dbService.document.update({
        where: { id: documentId },
        data: {
          status: 'ERROR',
          errorMessage: `Failed to retrieve file: ${downloadError?.message || 'File not found'}`
        }
      })

      return NextResponse.json({
        error: "Failed to retrieve file from storage",
        details: downloadError?.message
      }, { status: 500 })
    }

    // Convert Blob to Buffer and store temporarily
    const arrayBuffer = await fileData.arrayBuffer()
    const buffer = Buffer.from(arrayBuffer)

    let tempFileId: string | null = null

    try {
      // Store buffer as temporary file to reduce memory usage in job queue
      tempFileId = await TempFileService.storeBuffer(buffer, document.fileName)

      // Add job to queue with priority based on file size (smaller files get higher priority)
      const priority = Math.max(1, 10 - Math.floor(buffer.length / (1024 * 1024)))

      const jobId = await jobQueue.addJob({
        type: 'DOCUMENT_PROCESSING',
        data: {
          documentId,
          userId: session.user.id,
          fileName: document.fileName,
          tempFileId, // Pass file reference instead of buffer
          fileSize: buffer.length
        },
        priority,
        maxAttempts: 3
      })

      // Mark as processing in cache with the actual job ID
      cacheService.setProcessing(documentId, jobId)

      console.log(`Document ${documentId} queued for processing with job ID ${jobId} (temp file: ${tempFileId})`)

      return NextResponse.json({
        success: true,
        message: "Document processing queued",
        status: 'PROCESSING',
        jobId
      })
    } catch (jobError) {
      // Clean up temporary file if job creation failed
      if (tempFileId) {
        await TempFileService.deleteFile(tempFileId)
      }
      throw jobError
    }

  } catch (error) {
    console.error("Document processing error:", error)

    const processedError = ErrorHandler.categorizeError(error)

    return NextResponse.json({
      error: processedError.userMessage,
      details: processedError.message,
      retryable: processedError.retryable,
      code: processedError.code
    }, { status: 500 })
  }
}

/**
 * Get document processing status
 */
export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient()
    const {
      data: { session },
    } = await supabase.auth.getSession()

    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const url = new URL(request.url)
    const documentId = url.searchParams.get('documentId')
    const jobId = url.searchParams.get('jobId')

    if (!documentId) {
      return NextResponse.json({ error: "Document ID is required" }, { status: 400 })
    }

    // Check job status if job ID is provided
    if (jobId) {
      const job = jobQueue.getJob(jobId)

      if (job) {
        return NextResponse.json({
          jobId,
          status: job.status,
          attempts: job.attempts,
          error: job.error
        })
      }
    }

    // Check cache first
    const cachedDocument = cacheService.getDocument(documentId)
    if (cachedDocument) {
      return NextResponse.json({
        documentId,
        status: cachedDocument.status,
        totalChunks: cachedDocument.totalChunks,
        fromCache: true
      })
    }

    // Fall back to database
    const document = await dbService.document.findFirst({
      where: {
        id: documentId,
        userId: session.user.id
      }
    })

    if (!document) {
      return NextResponse.json({ error: "Document not found" }, { status: 404 })
    }

    return NextResponse.json({
      documentId,
      status: document.status,
      totalChunks: document.totalChunks,
      errorMessage: document.errorMessage,
      fromCache: false
    })

  } catch (error) {
    console.error("Error getting document status:", error)

    return NextResponse.json({
      error: "Failed to get document status",
      details: error instanceof Error ? error.message : "Unknown error"
    }, { status: 500 })
  }
}
