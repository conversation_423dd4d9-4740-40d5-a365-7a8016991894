/**
 * AI Response Cache
 * Implements intelligent caching for AI responses to improve performance
 */

// Use Web Crypto API instead of Node.js crypto for Vercel compatibility

export interface CacheEntry {
    key: string
    response: string
    timestamp: number
    chunkIndex: number
    documentId: string
    queryHash: string
    hitCount: number
    lastAccessed: number
}

export interface CacheConfig {
    maxEntries: number
    ttlMs: number // Time to live in milliseconds
    similarityThreshold: number // 0-1, how similar queries need to be to match
    enableSimilarityMatching: boolean
}

export class ResponseCache {
    private cache = new Map<string, CacheEntry>()
    private config: CacheConfig

    constructor(config?: Partial<CacheConfig>) {
        this.config = {
            maxEntries: 1000,
            ttlMs: 30 * 60 * 1000, // 30 minutes
            similarityThreshold: 0.8,
            enableSimilarityMatching: true,
            ...config
        }
    }

    /**
     * Generate cache key from query and context
     */
    private generateCacheKey(
        query: string,
        documentId: string,
        chunkIndex: number
    ): string {
        const normalizedQuery = this.normalizeQuery(query)
        const content = `${documentId}:${chunkIndex}:${normalizedQuery}`
        // Use simple hash for Vercel compatibility
        return this.simpleHash(content)
    }

    /**
     * Generate query hash for similarity matching
     */
    private generateQueryHash(query: string): string {
        const normalizedQuery = this.normalizeQuery(query)
        // Use simple hash for Vercel compatibility
        return this.simpleHash(normalizedQuery)
    }

    /**
     * Simple hash function for Vercel compatibility (replaces Node.js crypto)
     */
    private simpleHash(str: string): string {
        let hash = 0
        if (str.length === 0) return hash.toString()
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i)
            hash = ((hash << 5) - hash) + char
            hash = hash & hash // Convert to 32-bit integer
        }
        return Math.abs(hash).toString(16)
    }

    /**
     * Normalize query for consistent caching
     */
    private normalizeQuery(query: string): string {
        return query
            .toLowerCase()
            .trim()
            .replace(/[^\w\s]/g, '') // Remove punctuation
            .replace(/\s+/g, ' ') // Normalize whitespace
    }



    /**
     * Find similar cached responses
     */
    private findSimilarResponse(
        query: string,
        documentId: string,
        chunkIndex: number
    ): CacheEntry | null {
        if (!this.config.enableSimilarityMatching) {
            return null
        }

        let bestMatch: CacheEntry | null = null
        let bestSimilarity = 0

        for (const entry of this.cache.values()) {
            // Only match within same document and chunk
            if (entry.documentId !== documentId || entry.chunkIndex !== chunkIndex) {
                continue
            }

            // Skip expired entries
            if (this.isExpired(entry)) {
                continue
            }

            // Calculate similarity (we need to reverse-engineer the original query)
            // For now, we'll use a simple approach - in production, you might store original queries
            const similarity = this.calculateQuerySimilarity(query, entry.queryHash)
            
            if (similarity > this.config.similarityThreshold && similarity > bestSimilarity) {
                bestSimilarity = similarity
                bestMatch = entry
            }
        }

        return bestMatch
    }

    /**
     * Calculate similarity using query hash (simplified approach)
     */
    private calculateQuerySimilarity(query: string, storedQueryHash: string): number {
        const currentQueryHash = this.generateQueryHash(query)
        
        // Simple hash-based similarity (not perfect, but fast)
        if (currentQueryHash === storedQueryHash) {
            return 1.0
        }
        
        // For more sophisticated similarity, we'd need to store original queries
        // This is a simplified implementation
        return 0
    }

    /**
     * Check if cache entry is expired
     */
    private isExpired(entry: CacheEntry): boolean {
        return Date.now() - entry.timestamp > this.config.ttlMs
    }

    /**
     * Clean up expired entries
     */
    private cleanup(): void {
        const expiredKeys: string[] = []

        for (const [key, entry] of this.cache.entries()) {
            if (this.isExpired(entry)) {
                expiredKeys.push(key)
            }
        }

        expiredKeys.forEach(key => this.cache.delete(key))
        
        // If still over limit, remove least recently used entries
        if (this.cache.size > this.config.maxEntries) {
            const entries = Array.from(this.cache.entries())
                .sort(([, a], [, b]) => a.lastAccessed - b.lastAccessed)
            
            const toRemove = entries.slice(0, this.cache.size - this.config.maxEntries)
            toRemove.forEach(([key]) => this.cache.delete(key))
        }

        console.log(`🧹 Cache cleanup: removed ${expiredKeys.length} expired entries, ${this.cache.size} entries remaining`)
    }

    /**
     * Get cached response
     */
    get(
        query: string,
        documentId: string,
        chunkIndex: number
    ): string | null {
        // Clean up periodically
        if (Math.random() < 0.1) { // 10% chance
            this.cleanup()
        }

        // Try exact match first
        const exactKey = this.generateCacheKey(query, documentId, chunkIndex)
        const exactMatch = this.cache.get(exactKey)
        
        if (exactMatch && !this.isExpired(exactMatch)) {
            exactMatch.hitCount++
            exactMatch.lastAccessed = Date.now()
            console.log(`🎯 Cache hit (exact): ${query.substring(0, 50)}...`)
            return exactMatch.response
        }

        // Try similarity match
        const similarMatch = this.findSimilarResponse(query, documentId, chunkIndex)
        if (similarMatch) {
            similarMatch.hitCount++
            similarMatch.lastAccessed = Date.now()
            console.log(`🎯 Cache hit (similar): ${query.substring(0, 50)}...`)
            return similarMatch.response
        }

        console.log(`❌ Cache miss: ${query.substring(0, 50)}...`)
        return null
    }

    /**
     * Store response in cache
     */
    set(
        query: string,
        response: string,
        documentId: string,
        chunkIndex: number
    ): void {
        const key = this.generateCacheKey(query, documentId, chunkIndex)
        const queryHash = this.generateQueryHash(query)
        
        const entry: CacheEntry = {
            key,
            response,
            timestamp: Date.now(),
            chunkIndex,
            documentId,
            queryHash,
            hitCount: 0,
            lastAccessed: Date.now()
        }

        this.cache.set(key, entry)
        console.log(`💾 Cached response: ${query.substring(0, 50)}...`)

        // Cleanup if needed
        if (this.cache.size > this.config.maxEntries * 1.1) {
            this.cleanup()
        }
    }

    /**
     * Clear cache for specific document
     */
    clearDocument(documentId: string): void {
        const keysToDelete: string[] = []
        
        for (const [key, entry] of this.cache.entries()) {
            if (entry.documentId === documentId) {
                keysToDelete.push(key)
            }
        }

        keysToDelete.forEach(key => this.cache.delete(key))
        console.log(`🗑️ Cleared ${keysToDelete.length} cache entries for document ${documentId}`)
    }

    /**
     * Clear entire cache
     */
    clear(): void {
        const size = this.cache.size
        this.cache.clear()
        console.log(`🗑️ Cleared entire cache (${size} entries)`)
    }

    /**
     * Get cache statistics
     */
    getStats(): {
        size: number
        maxEntries: number
        hitRate: number
        totalHits: number
        oldestEntry: number | null
        newestEntry: number | null
    } {
        const entries = Array.from(this.cache.values())
        const totalHits = entries.reduce((sum, entry) => sum + entry.hitCount, 0)
        const totalRequests = entries.length + totalHits // Approximation
        
        return {
            size: this.cache.size,
            maxEntries: this.config.maxEntries,
            hitRate: totalRequests > 0 ? totalHits / totalRequests : 0,
            totalHits,
            oldestEntry: entries.length > 0 ? Math.min(...entries.map(e => e.timestamp)) : null,
            newestEntry: entries.length > 0 ? Math.max(...entries.map(e => e.timestamp)) : null
        }
    }

    /**
     * Preload cache with common responses
     */
    preload(entries: Array<{
        query: string
        response: string
        documentId: string
        chunkIndex: number
    }>): void {
        entries.forEach(entry => {
            this.set(entry.query, entry.response, entry.documentId, entry.chunkIndex)
        })
        
        console.log(`🚀 Preloaded ${entries.length} cache entries`)
    }
}

// Export singleton instance
export const responseCache = new ResponseCache()
